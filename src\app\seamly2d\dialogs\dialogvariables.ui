<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogVariables</class>
 <widget class="QDialog" name="DialogVariables">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>775</width>
    <height>535</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>750</width>
    <height>535</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Variables</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/32x32/table.png</normaloff>:/icon/32x32/table.png</iconset>
  </property>
  <property name="locale">
   <locale language="English" country="UnitedStates"/>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <property name="topMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QLabel" name="label">
       <property name="maximumSize">
        <size>
         <width>16</width>
         <height>16</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap resource="../../../libs/vmisc/share/resources/icon.qrc">:/icon/32x32/filter.png</pixmap>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLabel" name="labelFind">
       <property name="text">
        <string>Filter:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="filter_LineEdit">
       <property name="placeholderText">
        <string>Filter list by keyword</string>
       </property>
       <property name="clearButtonEnabled">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>1</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="tabPosition">
      <enum>QTabWidget::North</enum>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="customVariables_Tab">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Custom variables</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QSplitter" name="splitter">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <widget class="QTableWidget" name="variables_TableWidget">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>8</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>150</height>
           </size>
          </property>
          <property name="alternatingRowColors">
           <bool>true</bool>
          </property>
          <property name="selectionMode">
           <enum>QAbstractItemView::SingleSelection</enum>
          </property>
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
          <property name="sortingEnabled">
           <bool>true</bool>
          </property>
          <property name="wordWrap">
           <bool>true</bool>
          </property>
          <attribute name="horizontalHeaderCascadingSectionResizes">
           <bool>false</bool>
          </attribute>
          <attribute name="horizontalHeaderMinimumSectionSize">
           <number>180</number>
          </attribute>
          <attribute name="horizontalHeaderDefaultSectionSize">
           <number>180</number>
          </attribute>
          <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
           <bool>true</bool>
          </attribute>
          <attribute name="horizontalHeaderStretchLastSection">
           <bool>true</bool>
          </attribute>
          <attribute name="verticalHeaderVisible">
           <bool>false</bool>
          </attribute>
          <attribute name="verticalHeaderCascadingSectionResizes">
           <bool>false</bool>
          </attribute>
          <attribute name="verticalHeaderMinimumSectionSize">
           <number>8</number>
          </attribute>
          <attribute name="verticalHeaderDefaultSectionSize">
           <number>25</number>
          </attribute>
          <attribute name="verticalHeaderStretchLastSection">
           <bool>false</bool>
          </attribute>
          <column>
           <property name="text">
            <string>Name</string>
           </property>
           <property name="background">
            <color>
             <red>210</red>
             <green>210</green>
             <blue>210</blue>
            </color>
           </property>
          </column>
          <column>
           <property name="text">
            <string>The calculated value</string>
           </property>
           <property name="background">
            <color>
             <red>210</red>
             <green>210</green>
             <blue>210</blue>
            </color>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Formula</string>
           </property>
           <property name="background">
            <color>
             <red>210</red>
             <green>210</green>
             <blue>210</blue>
            </color>
           </property>
          </column>
         </widget>
         <widget class="QWidget" name="layoutWidget">
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <property name="spacing">
            <number>6</number>
           </property>
           <item>
            <widget class="QToolButton" name="toolButtonUp">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="toolTip">
              <string>Move measurement up</string>
             </property>
             <property name="text">
              <string notr="true">...</string>
             </property>
             <property name="icon">
              <iconset resource="../../../libs/vmisc/share/resources/theme.qrc">
               <normaloff>:/icons/win.icon.theme/16x16/actions/go-up.png</normaloff>:/icons/win.icon.theme/16x16/actions/go-up.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QToolButton" name="toolButtonDown">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="toolTip">
              <string>Move measurement down</string>
             </property>
             <property name="text">
              <string notr="true">...</string>
             </property>
             <property name="icon">
              <iconset resource="../../../libs/vmisc/share/resources/theme.qrc">
               <normaloff>:/icons/win.icon.theme/16x16/actions/go-down.png</normaloff>:/icons/win.icon.theme/16x16/actions/go-down.png</iconset>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>5000</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QToolButton" name="formula_ToolButton">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="toolTip">
              <string>Formula wizard</string>
             </property>
             <property name="text">
              <string notr="true">...</string>
             </property>
             <property name="icon">
              <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
               <normaloff>:/icon/24x24/fx.png</normaloff>:/icon/24x24/fx.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>16</width>
               <height>16</height>
              </size>
             </property>
            </widget>
           </item>
           <item alignment="Qt::AlignRight">
            <widget class="QToolButton" name="addCustomVariable_ToolButton">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="toolTip">
              <string>Add custom variable</string>
             </property>
             <property name="text">
              <string notr="true">...</string>
             </property>
             <property name="icon">
              <iconset resource="../../../libs/vmisc/share/resources/theme.qrc">
               <normaloff>:/icons/win.icon.theme/24x24/actions/list-add.png</normaloff>:/icons/win.icon.theme/24x24/actions/list-add.png</iconset>
             </property>
            </widget>
           </item>
           <item alignment="Qt::AlignLeft">
            <widget class="QToolButton" name="removeCustomVariable_ToolButton">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="toolTip">
              <string>Remove custom variable</string>
             </property>
             <property name="text">
              <string notr="true">...</string>
             </property>
             <property name="icon">
              <iconset resource="../../../libs/vmisc/share/resources/theme.qrc">
               <normaloff>:/icons/win.icon.theme/24x24/actions/list-remove.png</normaloff>:/icons/win.icon.theme/24x24/actions/list-remove.png</iconset>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QGroupBox" name="groupBoxDetails">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
            <horstretch>0</horstretch>
            <verstretch>4</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>236</height>
           </size>
          </property>
          <property name="toolTip">
           <string>Details</string>
          </property>
          <property name="title">
           <string>Details</string>
          </property>
          <layout class="QFormLayout" name="formLayout">
           <item row="1" column="0">
            <widget class="QLabel" name="name_Label">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Name:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="name_LineEdit">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="placeholderText">
              <string>Unique variable name</string>
             </property>
            </widget>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="labelCalculated">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Calculated value:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLabel" name="calculatedValue_Label">
             <property name="text">
              <string notr="true"/>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <widget class="QLabel" name="formula_Label">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Formula:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="4" column="1">
            <layout class="QHBoxLayout" name="horizontalLayoutValue">
             <item>
              <widget class="QPlainTextEdit" name="formula_PlainTextEdit">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>50</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="toolTip">
                <string>Calculation</string>
               </property>
               <property name="tabChangesFocus">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="6" column="0">
            <widget class="QLabel" name="description_Label">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>100</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string>Description:</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
             </property>
            </widget>
           </item>
           <item row="6" column="1">
            <widget class="QPlainTextEdit" name="description_PlainTextEdit">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
               <horstretch>0</horstretch>
               <verstretch>1</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>50</height>
              </size>
             </property>
            </widget>
           </item>
           <item row="8" column="1">
            <spacer name="verticalSpacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="9" column="1">
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="refresh_PushButton">
               <property name="toolTip">
                <string>Refresh a pattern with all changes you made</string>
               </property>
               <property name="text">
                <string>Refresh</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="lineLength_Tab">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Line lengths</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_3">
       <item row="0" column="0">
        <widget class="QTableWidget" name="lineLengths_TableWidget">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
         <attribute name="horizontalHeaderCascadingSectionResizes">
          <bool>false</bool>
         </attribute>
         <attribute name="horizontalHeaderDefaultSectionSize">
          <number>137</number>
         </attribute>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>false</bool>
         </attribute>
         <attribute name="verticalHeaderVisible">
          <bool>false</bool>
         </attribute>
         <column>
          <property name="text">
           <string>Line</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Length</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="lineAngles_Tab">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Line angles</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_6">
       <item row="0" column="0">
        <widget class="QTableWidget" name="lineAngles_TableWidget">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
         <attribute name="horizontalHeaderCascadingSectionResizes">
          <bool>false</bool>
         </attribute>
         <attribute name="horizontalHeaderDefaultSectionSize">
          <number>137</number>
         </attribute>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>false</bool>
         </attribute>
         <attribute name="verticalHeaderVisible">
          <bool>false</bool>
         </attribute>
         <column>
          <property name="text">
           <string>Line</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Angle</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="curveLength_Tab">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Curve lengths</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_4">
       <item row="0" column="0">
        <widget class="QTableWidget" name="curveLengths_TableWidget">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
         <attribute name="horizontalHeaderCascadingSectionResizes">
          <bool>false</bool>
         </attribute>
         <attribute name="horizontalHeaderDefaultSectionSize">
          <number>137</number>
         </attribute>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>false</bool>
         </attribute>
         <attribute name="verticalHeaderVisible">
          <bool>false</bool>
         </attribute>
         <column>
          <property name="text">
           <string>Curve</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Length</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="curveAngles_Tab">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Curve angles</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_8">
       <item row="0" column="0">
        <widget class="QTableWidget" name="curveAngles_TableWidget">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
         <attribute name="horizontalHeaderCascadingSectionResizes">
          <bool>false</bool>
         </attribute>
         <attribute name="horizontalHeaderDefaultSectionSize">
          <number>137</number>
         </attribute>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>false</bool>
         </attribute>
         <attribute name="verticalHeaderVisible">
          <bool>false</bool>
         </attribute>
         <column>
          <property name="text">
           <string>Curve</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Angle</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="cpLength_Tab">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Control point lengths</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <widget class="QTableWidget" name="controlPointLengths_TableWidget">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
         <attribute name="horizontalHeaderCascadingSectionResizes">
          <bool>false</bool>
         </attribute>
         <attribute name="horizontalHeaderDefaultSectionSize">
          <number>137</number>
         </attribute>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>false</bool>
         </attribute>
         <attribute name="verticalHeaderVisible">
          <bool>false</bool>
         </attribute>
         <column>
          <property name="text">
           <string>Curve</string>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Length</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="arcRadius_Tab">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Arc radiuses</string>
      </attribute>
      <layout class="QGridLayout" name="gridLayout_7">
       <item row="0" column="0">
        <widget class="QTableWidget" name="arcRadiuses_TableWidget">
         <property name="editTriggers">
          <set>QAbstractItemView::NoEditTriggers</set>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
         <attribute name="horizontalHeaderCascadingSectionResizes">
          <bool>false</bool>
         </attribute>
         <attribute name="horizontalHeaderDefaultSectionSize">
          <number>137</number>
         </attribute>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>false</bool>
         </attribute>
         <attribute name="verticalHeaderVisible">
          <bool>false</bool>
         </attribute>
         <column>
          <property name="text">
           <string>Arc</string>
          </property>
          <property name="background">
           <color>
            <red>210</red>
            <green>210</green>
            <blue>210</blue>
           </color>
          </property>
         </column>
         <column>
          <property name="text">
           <string>Radius</string>
          </property>
         </column>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>variables_TableWidget</tabstop>
  <tabstop>addCustomVariable_ToolButton</tabstop>
  <tabstop>removeCustomVariable_ToolButton</tabstop>
  <tabstop>lineLengths_TableWidget</tabstop>
  <tabstop>curveLengths_TableWidget</tabstop>
 </tabstops>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
  <include location="../../../libs/vmisc/share/resources/theme.qrc"/>
 </resources>
 <connections/>
</ui>
