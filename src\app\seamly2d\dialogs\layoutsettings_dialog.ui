<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LayoutSettingsDialog</class>
 <widget class="QDialog" name="LayoutSettingsDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>533</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Layout print settings</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../share/resources/toolicon.qrc">
    <normaloff>:/toolicon/32x32/layout_settings.png</normaloff>:/toolicon/32x32/layout_settings.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_8">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <item>
        <widget class="QGroupBox" name="paperFormat_GroupBox">
         <property name="autoFillBackground">
          <bool>false</bool>
         </property>
         <property name="title">
          <string>Paper format</string>
         </property>
         <property name="flat">
          <bool>false</bool>
         </property>
         <property name="checkable">
          <bool>false</bool>
         </property>
         <property name="checked">
          <bool>false</bool>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <property name="spacing">
             <number>6</number>
            </property>
            <item>
             <widget class="QLabel" name="label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>Templates:</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="PageFormatCombobox" name="comboBoxTemplates"/>
            </item>
           </layout>
          </item>
          <item>
           <widget class="Line" name="line">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QGridLayout" name="gridLayout">
            <item row="2" column="0">
             <layout class="QHBoxLayout" name="horizontalLayout">
              <item>
               <widget class="QToolButton" name="portrait_ToolButton">
                <property name="text">
                 <string notr="true">...</string>
                </property>
                <property name="icon">
                 <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                  <normaloff>:/icon/16x16/portrait.png</normaloff>:/icon/16x16/portrait.png</iconset>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
                <property name="autoRaise">
                 <bool>true</bool>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">buttonGroup</string>
                </attribute>
               </widget>
              </item>
              <item>
               <widget class="QToolButton" name="landscape_ToolButton">
                <property name="text">
                 <string notr="true">...</string>
                </property>
                <property name="icon">
                 <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                  <normaloff>:/icon/16x16/landscape.png</normaloff>:/icon/16x16/landscape.png</iconset>
                </property>
                <property name="checkable">
                 <bool>true</bool>
                </property>
                <property name="checked">
                 <bool>false</bool>
                </property>
                <property name="autoRaise">
                 <bool>true</bool>
                </property>
                <attribute name="buttonGroup">
                 <string notr="true">buttonGroup</string>
                </attribute>
               </widget>
              </item>
             </layout>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="labelWidth">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>Width:</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="labelHeight">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>Height:</string>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QComboBox" name="comboBoxPaperSizeUnit">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>124</width>
                <height>0</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QDoubleSpinBox" name="doubleSpinBoxPaperHeight">
              <property name="minimumSize">
               <size>
                <width>94</width>
                <height>0</height>
               </size>
              </property>
              <property name="decimals">
               <number>2</number>
              </property>
              <property name="maximum">
               <double>99999.000000000000000</double>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QDoubleSpinBox" name="doubleSpinBoxPaperWidth">
              <property name="minimumSize">
               <size>
                <width>94</width>
                <height>0</height>
               </size>
              </property>
              <property name="decimals">
               <number>2</number>
              </property>
              <property name="maximum">
               <double>99999.990000000005239</double>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <item>
          <widget class="QLabel" name="label_6">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Printer:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBoxPrinter"/>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBoxPaperFileds">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="title">
          <string>Margins</string>
         </property>
         <layout class="QFormLayout" name="formLayout">
          <property name="fieldGrowthPolicy">
           <enum>QFormLayout::ExpandingFieldsGrow</enum>
          </property>
          <item row="1" column="0">
           <widget class="QLabel" name="labelLeftField">
            <property name="text">
             <string>Left:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QDoubleSpinBox" name="leftField_DoubleSpinBox">
            <property name="decimals">
             <number>5</number>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="labelRightField">
            <property name="text">
             <string>Right:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QDoubleSpinBox" name="rightField_DoubleSpinBox">
            <property name="decimals">
             <number>5</number>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="labelTopField">
            <property name="text">
             <string>Top:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QDoubleSpinBox" name="topField_DoubleSpinBox">
            <property name="decimals">
             <number>5</number>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QLabel" name="labelBottomField">
            <property name="text">
             <string>Bottom:</string>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <widget class="QDoubleSpinBox" name="bottomField_DoubleSpinBox">
            <property name="decimals">
             <number>5</number>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QCheckBox" name="checkBoxIgnoreFileds">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="text">
          <string>Ignore margins</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_7">
         <item>
          <widget class="QGroupBox" name="groupBoxText">
           <property name="title">
            <string>Text</string>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_6">
            <item>
             <widget class="QCheckBox" name="textAsPaths_Checkbox">
              <property name="toolTip">
               <string>Text will be converted to paths</string>
              </property>
              <property name="text">
               <string>Export text as paths</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
     <item>
      <widget class="Line" name="line_6">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="groupBoxCreationOptions">
       <property name="title">
        <string>Layout options</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <item>
         <layout class="QGridLayout" name="gridLayout_2">
          <item row="0" column="1">
           <widget class="QDoubleSpinBox" name="doubleSpinBoxLayoutWidth">
            <property name="minimumSize">
             <size>
              <width>94</width>
              <height>0</height>
             </size>
            </property>
            <property name="minimum">
             <double>0.000000000000000</double>
            </property>
            <property name="maximum">
             <double>99999.990000000005239</double>
            </property>
            <property name="value">
             <double>1.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="0" column="0">
           <widget class="QLabel" name="labelLayoutWidth">
            <property name="text">
             <string>Gap width:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="labelShiftLength">
            <property name="text">
             <string>Shift/Offset length:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QDoubleSpinBox" name="doubleSpinBoxShift">
            <property name="minimumSize">
             <size>
              <width>94</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximum">
             <double>99999.990000000005239</double>
            </property>
            <property name="value">
             <double>0.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="1" column="2">
           <widget class="QComboBox" name="comboBoxLayoutUnit"/>
          </item>
          <item row="0" column="2">
           <widget class="QLabel" name="label_4">
            <property name="text">
             <string notr="true">×2</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxSaveLength">
          <property name="text">
           <string>Save length of the sheet</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="Line" name="line_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxRotate">
          <property name="title">
           <string>Rotate workpiece</string>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string>Rotate by</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QComboBox" name="comboBoxIncrease">
             <property name="currentIndex">
              <number>21</number>
             </property>
             <item>
              <property name="text">
               <string notr="true">1</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">2</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">3</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">4</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">5</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">6</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">8</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">9</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">10</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">12</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">15</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">18</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">20</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">24</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">30</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">36</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">40</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">45</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">60</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">72</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">90</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string notr="true">180</string>
              </property>
             </item>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_3">
             <property name="text">
              <string>degree</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="Line" name="line_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxCase">
          <property name="title">
           <string>Rule for choosing the next workpiece</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_2">
             <item>
              <widget class="QRadioButton" name="radioButtonThreeGroups">
               <property name="text">
                <string>Three groups: big, middle, small</string>
               </property>
               <attribute name="buttonGroup">
                <string notr="true">buttonGroupPrinciple</string>
               </attribute>
              </widget>
             </item>
             <item>
              <widget class="QRadioButton" name="radioButtonTwoGroups">
               <property name="text">
                <string>Two groups: big, small</string>
               </property>
               <property name="checked">
                <bool>false</bool>
               </property>
               <attribute name="buttonGroup">
                <string notr="true">buttonGroupPrinciple</string>
               </attribute>
              </widget>
             </item>
             <item>
              <widget class="QRadioButton" name="radioButtonDescendingArea">
               <property name="text">
                <string>Descending area</string>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
               <attribute name="buttonGroup">
                <string notr="true">buttonGroupPrinciple</string>
               </attribute>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="Line" name="line_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxAutoCrop">
          <property name="text">
           <string>Auto crop unused length</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxUnitePages">
          <property name="text">
           <string>Unite pages (if possible)</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="Line" name="line_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxStrips">
          <property name="toolTip">
           <string>Enabling for sheets that have big height will speed up creating.</string>
          </property>
          <property name="title">
           <string>Divide into strips</string>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <item>
            <widget class="QLabel" name="label_5">
             <property name="text">
              <string>Multiplier</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QSpinBox" name="spinBoxMultiplier">
             <property name="toolTip">
              <string>Set multiplier for length of the biggest workpiece in layout.</string>
             </property>
             <property name="prefix">
              <string notr="true">x</string>
             </property>
             <property name="minimum">
              <number>1</number>
             </property>
             <property name="maximum">
              <number>10</number>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok|QDialogButtonBox::RestoreDefaults</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>PageFormatCombobox</class>
   <extends>QComboBox</extends>
   <header>page_format_combobox.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../share/resources/toolicon.qrc"/>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>LayoutSettingsDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>LayoutSettingsDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <buttongroups>
  <buttongroup name="buttonGroup"/>
  <buttongroup name="buttonGroupPrinciple"/>
 </buttongroups>
</ui>
