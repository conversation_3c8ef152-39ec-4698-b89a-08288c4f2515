<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ImageDialog</class>
 <widget class="QDialog" name="ImageDialog">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>600</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>350</width>
    <height>600</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>550</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Image Properties</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../vmisc/share/resources/theme.qrc">
    <normaloff>:/icons/win.icon.theme/16x16/actions/preferences-other.png</normaloff>:/icons/win.icon.theme/16x16/actions/preferences-other.png</iconset>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QGroupBox" name="selection_GroupBox">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>328</width>
       <height>85</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Selection</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <item>
       <layout class="QFormLayout" name="formLayout_2">
        <property name="labelAlignment">
         <set>Qt::AlignBottom|Qt::AlignRight|Qt::AlignTrailing</set>
        </property>
        <property name="formAlignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <item row="1" column="0">
         <widget class="QLabel" name="id_Label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Id:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLabel" name="idText_Label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Id</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="name_Label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>10</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Name:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="lockImage_Label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Lock Image:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QToolButton" name="lockImage_ToolButton">
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
          <property name="toolTip">
           <string notr="true"/>
          </property>
          <property name="styleSheet">
           <string notr="true">QToolButton:checked {
    background-color: #f0f0f0;
}
QToolButton:!checked {
    background-color: #f0f0f0;
}
</string>
          </property>
          <property name="text">
           <string notr="true">...</string>
          </property>
          <property name="icon">
           <iconset resource="../../vmisc/share/resources/icon.qrc">
            <normaloff>:/icon/32x32/lock_off.png</normaloff>
            <normalon>:/icon/32x32/lock_on.png</normalon>:/icon/32x32/lock_off.png</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>18</width>
            <height>18</height>
           </size>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLineEdit" name="name_LineEdit">
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3"/>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="geometry_GroupBox">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Geometry</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="1">
         <widget class="QToolButton" name="units_ToolButton">
          <property name="toolTip">
           <string>Switch between px and pattern units</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QToolButton:checked {
    background-color: #f0f0f0;
}
QToolButton:!checked {
    background-color: #f0f0f0;
}
</string>
          </property>
          <property name="text">
           <string notr="true">PX</string>
          </property>
          <property name="icon">
           <iconset resource="../../vmisc/share/resources/icon.qrc">
            <normaloff>:/icon/32x32/units_in_on.png</normaloff>
            <normalon>:/icon/32x32/units_px_on.png</normalon>
            <selectedoff>:/icon/32x32/units_in_on.png</selectedoff>
            <selectedon>:/icon/32x32/units_px_on.png</selectedon>:/icon/32x32/units_in_on.png</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>18</width>
            <height>18</height>
           </size>
          </property>
          <property name="checkable">
           <bool>false</bool>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_2">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>Unit:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <widget class="Line" name="line_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QFormLayout" name="formLayout_4">
        <property name="labelAlignment">
         <set>Qt::AlignBottom|Qt::AlignRight|Qt::AlignTrailing</set>
        </property>
        <property name="formAlignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <item row="0" column="0">
         <widget class="QLabel" name="xPosition_Label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>X Position:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QDoubleSpinBox" name="xPosition_DoubleSpinBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>75</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="keyboardTracking">
           <bool>false</bool>
          </property>
          <property name="suffix">
           <string notr="true">px</string>
          </property>
          <property name="decimals">
           <number>0</number>
          </property>
          <property name="minimum">
           <double>-16000.000000000000000</double>
          </property>
          <property name="maximum">
           <double>16000.000000000000000</double>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="yPosition_Label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Y Position:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QDoubleSpinBox" name="yPosition_DoubleSpinBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>75</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="keyboardTracking">
           <bool>false</bool>
          </property>
          <property name="suffix">
           <string notr="true">px</string>
          </property>
          <property name="decimals">
           <number>0</number>
          </property>
          <property name="minimum">
           <double>-16000.000000000000000</double>
          </property>
          <property name="maximum">
           <double>16000.000000000000000</double>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="Line" name="line">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout">
        <item row="0" column="1">
         <widget class="QToolButton" name="aspectLocked_ToolButton">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="toolTip">
           <string>Lock aspect ratio</string>
          </property>
          <property name="styleSheet">
           <string notr="true">QToolButton:checked {
    background-color: #f0f0f0;
}
QToolButton:!checked {
    background-color: #f0f0f0;
}
</string>
          </property>
          <property name="text">
           <string notr="true">...</string>
          </property>
          <property name="icon">
           <iconset resource="../../vmisc/share/resources/icon.qrc">
            <normaloff>:/icon/32x32/lock_off.png</normaloff>
            <normalon>:/icon/32x32/lock_on.png</normalon>:/icon/32x32/lock_off.png</iconset>
          </property>
          <property name="iconSize">
           <size>
            <width>18</width>
            <height>18</height>
           </size>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="lockAspectRatio_Label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>20</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Lock Aspect:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="wordWrap">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="0" column="2">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <item>
         <layout class="QFormLayout" name="formLayout_5">
          <property name="labelAlignment">
           <set>Qt::AlignBottom|Qt::AlignRight|Qt::AlignTrailing</set>
          </property>
          <property name="formAlignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
          <item row="0" column="0">
           <widget class="QLabel" name="width_Label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>20</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Width:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QDoubleSpinBox" name="width_DoubleSpinBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>75</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <bold>false</bold>
             </font>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
            <property name="keyboardTracking">
             <bool>false</bool>
            </property>
            <property name="suffix">
             <string notr="true">px</string>
            </property>
            <property name="decimals">
             <number>0</number>
            </property>
            <property name="minimum">
             <double>0.000000000000000</double>
            </property>
            <property name="maximum">
             <double>99999.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="height_Label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>10</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Height:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QDoubleSpinBox" name="height_DoubleSpinBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>75</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <bold>false</bold>
             </font>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
            <property name="keyboardTracking">
             <bool>false</bool>
            </property>
            <property name="suffix">
             <string notr="true">px</string>
            </property>
            <property name="decimals">
             <number>0</number>
            </property>
            <property name="minimum">
             <double>0.000000000000000</double>
            </property>
            <property name="maximum">
             <double>99999.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="xScale_Label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>10</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>X Scale:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QDoubleSpinBox" name="xScale_DoubleSpinBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>75</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <bold>false</bold>
             </font>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
            <property name="keyboardTracking">
             <bool>false</bool>
            </property>
            <property name="suffix">
             <string notr="true">%</string>
            </property>
            <property name="decimals">
             <number>2</number>
            </property>
            <property name="maximum">
             <double>99999.000000000000000</double>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="yScale_Label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>10</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Y Scale:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QDoubleSpinBox" name="yScale_DoubleSpinBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>75</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <bold>false</bold>
             </font>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
            <property name="keyboardTracking">
             <bool>false</bool>
            </property>
            <property name="suffix">
             <string notr="true">%</string>
            </property>
            <property name="decimals">
             <number>2</number>
            </property>
            <property name="maximum">
             <double>99999.000000000000000</double>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <widget class="Line" name="line_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QFormLayout" name="formLayout_6">
        <property name="labelAlignment">
         <set>Qt::AlignBottom|Qt::AlignRight|Qt::AlignTrailing</set>
        </property>
        <property name="formAlignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <item row="0" column="0">
         <widget class="QLabel" name="rotation_Label_7">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Rotation:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QDoubleSpinBox" name="rotation_DoubleSpinBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>75</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="keyboardTracking">
           <bool>false</bool>
          </property>
          <property name="suffix">
           <string notr="true">°</string>
          </property>
          <property name="decimals">
           <number>3</number>
          </property>
          <property name="minimum">
           <double>-360.000000000000000</double>
          </property>
          <property name="maximum">
           <double>360.000000000000000</double>
          </property>
          <property name="singleStep">
           <double>1.000000000000000</double>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="attributes_GroupBox">
     <property name="font">
      <font>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Attributes</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>7</number>
      </property>
      <item>
       <layout class="QFormLayout" name="formLayout_7">
        <property name="labelAlignment">
         <set>Qt::AlignBottom|Qt::AlignRight|Qt::AlignTrailing</set>
        </property>
        <property name="formAlignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <item row="0" column="0">
         <widget class="QLabel" name="opacity_Label">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>9</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Opacity:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QDoubleSpinBox" name="opacity_DoubleSpinBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>75</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="suffix">
           <string notr="true">%</string>
          </property>
          <property name="decimals">
           <number>0</number>
          </property>
          <property name="minimum">
           <double>0.000000000000000</double>
          </property>
          <property name="value">
           <double>100.000000000000000</double>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Apply|QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../vmisc/share/resources/theme.qrc"/>
  <include location="../../vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>ImageDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>ImageDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
