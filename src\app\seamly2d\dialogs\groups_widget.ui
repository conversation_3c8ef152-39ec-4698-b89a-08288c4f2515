<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GroupsWidget</class>
 <widget class="QWidget" name="GroupsWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>270</width>
    <height>483</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>270</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string notr="true"/>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/64x64/icon64x64.png</normaloff>:/icon/64x64/icon64x64.png</iconset>
  </property>
  <property name="toolTip">
   <string>Group Manager</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QHBoxLayout" name="groupTools_HorizontalLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QToolButton" name="showAllGroups_ToolButton">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>Show All Groups</string>
       </property>
       <property name="icon">
        <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
         <normaloff>:/icon/32x32/visible_on.png</normaloff>:/icon/32x32/visible_on.png</iconset>
       </property>
       <property name="autoRaise">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="hideAllGroups_ToolButton">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>Hide all groups</string>
       </property>
       <property name="statusTip">
        <string>Hide all groups</string>
       </property>
       <property name="icon">
        <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
         <normaloff>:/icon/32x32/visible_off.png</normaloff>:/icon/32x32/visible_off.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="lockAllGroups_ToolButton">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>Lock all groups</string>
       </property>
       <property name="statusTip">
        <string>Lock all groups</string>
       </property>
       <property name="icon">
        <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
         <normaloff>:/icon/32x32/lock_on.png</normaloff>:/icon/32x32/lock_on.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="unlockAllGroups_ToolButton">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>Unlock all groups</string>
       </property>
       <property name="statusTip">
        <string>Unlock all groups</string>
       </property>
       <property name="icon">
        <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
         <normaloff>:/icon/32x32/lock_off.png</normaloff>:/icon/32x32/lock_off.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="addGroup_ToolButton">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>Add a new group to the list</string>
       </property>
       <property name="autoFillBackground">
        <bool>false</bool>
       </property>
       <property name="icon">
        <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
         <normaloff>:/icon/32x32/add.png</normaloff>:/icon/32x32/add.png</iconset>
       </property>
       <property name="checkable">
        <bool>true</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="deleteGroup_ToolButton">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>Delete active group from the list</string>
       </property>
       <property name="icon">
        <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
         <normaloff>:/icon/32x32/remove.png</normaloff>:/icon/32x32/remove.png</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="editGroup_ToolButton">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>Edit group properties</string>
       </property>
       <property name="icon">
        <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
         <normaloff>:/icon/32x32/edit.png</normaloff>:/icon/32x32/edit.png</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QSplitter" name="groups_Splitter">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
          <property name="childrenCollapsible">
      <bool>false</bool>
     </property>
     <widget class="QGroupBox" name="groups_GroupBox">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>100</height>
       </size>
      </property>
      <property name="title">
       <string>Groups</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <item>
        <widget class="QTableWidget" name="groups_TableWidget">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="MinimumExpanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>100</height>
          </size>
         </property>
         <property name="palette">
          <palette>
           <active>
            <colorrole role="Highlight">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>184</red>
               <green>212</green>
               <blue>235</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="HighlightedText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </active>
           <inactive>
            <colorrole role="Highlight">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>215</red>
               <green>215</green>
               <blue>215</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="HighlightedText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </inactive>
           <disabled>
            <colorrole role="Highlight">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>208</red>
               <green>208</green>
               <blue>208</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="HighlightedText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </disabled>
          </palette>
         </property>
         <property name="toolTip">
          <string>Group list</string>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="sizeAdjustPolicy">
          <enum>QAbstractScrollArea::AdjustToContents</enum>
         </property>
         <property name="alternatingRowColors">
          <bool>true</bool>
         </property>
         <property name="selectionMode">
          <enum>QAbstractItemView::SingleSelection</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectRows</enum>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
         <property name="columnCount">
          <number>5</number>
         </property>
         <attribute name="horizontalHeaderVisible">
          <bool>true</bool>
         </attribute>
         <attribute name="horizontalHeaderMinimumSectionSize">
          <number>16</number>
         </attribute>
         <attribute name="horizontalHeaderDefaultSectionSize">
          <number>30</number>
         </attribute>
         <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
          <bool>true</bool>
         </attribute>
         <attribute name="horizontalHeaderStretchLastSection">
          <bool>true</bool>
         </attribute>
         <attribute name="verticalHeaderVisible">
          <bool>false</bool>
         </attribute>
         <attribute name="verticalHeaderMinimumSectionSize">
          <number>10</number>
         </attribute>
         <attribute name="verticalHeaderHighlightSections">
          <bool>false</bool>
         </attribute>
         <column/>
         <column/>
         <column/>
         <column/>
         <column/>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QGroupBox" name="objects_GroupBox">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="minimumSize">
       <size>
        <width>250</width>
        <height>0</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>16777215</height>
       </size>
      </property>
      <property name="toolTip">
       <string>Group object list</string>
      </property>
      <property name="title">
       <string>Objects</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QListWidget" name="groupItems_ListWidget">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="palette">
          <palette>
           <active>
            <colorrole role="Button">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Base">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Window">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Highlight">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>184</red>
               <green>212</green>
               <blue>235</blue>
              </color>
             </brush>
            </colorrole>
           </active>
           <inactive>
            <colorrole role="Button">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Base">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Window">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Highlight">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>184</red>
               <green>212</green>
               <blue>235</blue>
              </color>
             </brush>
            </colorrole>
           </inactive>
           <disabled>
            <colorrole role="Button">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Base">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Window">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>230</red>
               <green>230</green>
               <blue>230</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Highlight">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>120</green>
               <blue>215</blue>
              </color>
             </brush>
            </colorrole>
           </disabled>
          </palette>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
          </font>
         </property>
         <property name="toolTip">
          <string>Double clicking zooms to object.</string>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(230, 230, 230);
</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::Panel</enum>
         </property>
         <property name="lineWidth">
          <number>2</number>
         </property>
         <property name="midLineWidth">
          <number>1</number>
         </property>
         <property name="sizeAdjustPolicy">
          <enum>QAbstractScrollArea::AdjustToContents</enum>
         </property>
         <property name="iconSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="sortingEnabled">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections/>
</ui>
