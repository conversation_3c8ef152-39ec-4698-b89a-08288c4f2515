<RCC>
    <qresource prefix="/">
        <file>cursor/endline_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/line_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/alongline_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/shoulder_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/normal_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/bisector_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/intersect_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/spline_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/arc_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/splinepath_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/new_piece_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/height_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/triangle_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/pointofintersect_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/spline_cut_point_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/splinepath_cut_point_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/union_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/arc_cut_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/cursor-arrow-closehand.png</file>
        <file>cursor/cursor-arrow-openhand.png</file>
        <file>cursor/line_intersect_axis_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/arc_intersect_axis_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/curve_intersect_axis_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/point_of_intersection_arcs.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/point_of_intersection_circles.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/point_from_circle_and_tangent_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/point_from_arc_and_tangent_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/arc_with_length_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/true_darts_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/intersection_curves_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/cubic_bezier_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/cubic_bezier_path_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/rotation_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/midpoint_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/<EMAIL></file>
        <file>cursor/mirror_by_line_cursor.png</file>
        <file>cursor/mirror_by_axis_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/move_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/el_arc_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/magnifier_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/anchor_point_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/path_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/group_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/insert_nodes_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/arrow_resize_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/point_intersect_arc_line_cursor.png</file>
        <file>cursor/<EMAIL></file>
        <file>cursor/image_origin_cursor.png</file>
    </qresource>
</RCC>
