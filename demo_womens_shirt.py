#!/usr/bin/env python3
"""
女式衬衫版型生成演示
生成包含前片、后片、袖子和领子的完整女式衬衫版型，并标记详细尺寸
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from seamly2d_ai_designer.garments.shirt import WomensShirt
from seamly2d_ai_designer.core.config import PatternConfig

def main():
    """主函数 - 演示女式衬衫版型生成"""
    print("🎯 女式衬衫版型生成演示")
    print("=" * 50)
    
    # 创建输出目录
    output_dir = project_root / "output"
    output_dir.mkdir(exist_ok=True)
    
    # 女性标准测量数据 (中等身材)
    measurements = {
        "chest_width": 44.0,    # 胸宽 (胸围/2) (cm)
        "bust": 88.0,           # 胸围 (cm)
        "waist": 68.0,          # 腰围 (cm)
        "shoulder_width": 38.0,  # 肩宽 (cm)
        "length": 65.0,         # 衣长 (cm)
        "neck_circumference": 36.0,  # 领围 (cm)
        "arm_length": 58.0,     # 袖长 (cm)
    }
    
    print("📏 使用的测量数据:")
    for key, value in measurements.items():
        print(f"   {key}: {value} cm")
    print()
    
    # 创建版型配置
    config = PatternConfig()
    config.author = "Seamly2D AI Designer"
    config.description = "女式衬衫版型 - 包含前片、后片、袖子和领子"
    config.version = "1.0.0"
    
    # 创建女式衬衫版型生成器
    shirt = WomensShirt(config)
    
    # 设置测量数据和参数
    shirt.set_measurements(measurements)
    shirt.set_ease(8.0)          # 衬衫放松量较大
    shirt.set_sleeve_length(58.0) # 长袖
    shirt.set_dart_length(12.0)   # 胸省长度
    
    # 生成版型文件
    output_file = output_dir / "womens_shirt_pattern.sm2d"
    print(f"🔧 正在生成女式衬衫版型...")
    
    try:
        pattern_file = shirt.create_pattern(str(output_file), preview=True)
        
        print(f"✅ 版型生成成功!")
        print(f"   📁 版型文件: {pattern_file}")
        
        # 检查预览图
        preview_file = str(output_file).replace(".sm2d", "_preview.png")
        if os.path.exists(preview_file):
            print(f"   🖼️  预览图: {preview_file}")
            
            # 获取文件大小
            pattern_size = os.path.getsize(pattern_file)
            preview_size = os.path.getsize(preview_file)
            
            print(f"\n📊 文件信息:")
            print(f"   版型文件大小: {pattern_size:,} 字节")
            print(f"   预览图大小: {preview_size:,} 字节")
        
        print(f"\n🎨 版型特点:")
        print(f"   • 包含前片（带胸省和门襟）")
        print(f"   • 包含后片")
        print(f"   • 包含长袖")
        print(f"   • 包含衬衫领")
        print(f"   • 详细的尺寸标注")
        print(f"   • 专业的制版规格")
        
        print(f"\n📐 版型参数:")
        print(f"   • 胸围放松量: {shirt.ease} cm")
        print(f"   • 前领深: {shirt.neck_depth_front} cm")
        print(f"   • 后领深: {shirt.neck_depth_back} cm")
        print(f"   • 袖窿深: {shirt.armhole_depth} cm")
        print(f"   • 胸省长度: {shirt.dart_length} cm")
        print(f"   • 领宽: {shirt.collar_width} cm")
        print(f"   • 门襟重叠: {shirt.button_overlap} cm")
        
        print(f"\n🎯 使用说明:")
        print(f"   1. 使用Seamly2D打开 {os.path.basename(pattern_file)}")
        print(f"   2. 版型已包含完整的前片、后片、袖子和领子")
        print(f"   3. 所有尺寸均已参数化，可根据需要调整")
        print(f"   4. 预览图显示了详细的尺寸标注")
        print(f"   5. 可导出为PDF或其他格式用于制作")
        
    except Exception as e:
        print(f"❌ 版型生成失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 