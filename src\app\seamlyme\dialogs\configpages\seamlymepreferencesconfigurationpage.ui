<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SeamlyMePreferencesConfigurationPage</class>
 <widget class="QWidget" name="SeamlyMePreferencesConfigurationPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>485</width>
    <height>567</height>
   </rect>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string notr="true">Configuration</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QGroupBox" name="startup_GroupBox">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Startup</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_7">
      <item>
       <widget class="QCheckBox" name="showWelcome_CheckBox">
        <property name="font">
         <font>
          <pointsize>10</pointsize>
          <bold>false</bold>
         </font>
        </property>
        <property name="text">
         <string>Do not show welcome screen</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="font">
      <font>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Language</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QLabel" name="label_2">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>GUI language:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="langCombo">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>300</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QLabel" name="label_3">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Decimal separator:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="osOption_CheckBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>300</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string notr="true">&lt; User locale &gt;</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_3">
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Pattern making system</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QLabel" name="label_6">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>System:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="systemCombo">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>300</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QLabel" name="label_7">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Author:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="systemAuthorValueLabel">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>300</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string notr="true">author</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <item>
         <widget class="QLabel" name="label_9">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Book:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTop|Qt::AlignTrailing</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPlainTextEdit" name="systemBookValueLabel">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>300</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox">
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Default height and size</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_6">
        <item>
         <widget class="QLabel" name="label">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Height:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="defHeightCombo">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>300</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_7">
        <item>
         <widget class="QLabel" name="label_4">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Size:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="defSizeCombo">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>300</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>10</pointsize>
            <bold>false</bold>
           </font>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_5">
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Measurements editing</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_6">
      <item>
       <widget class="QPushButton" name="resetWarningsButton">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="font">
         <font>
          <pointsize>10</pointsize>
          <bold>false</bold>
         </font>
        </property>
        <property name="text">
         <string>Reset warnings</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_6">
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <bold>true</bold>
      </font>
     </property>
     <property name="title">
      <string>Toolbar</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <item>
       <widget class="QCheckBox" name="toolBarStyle_CheckBox">
        <property name="font">
         <font>
          <pointsize>10</pointsize>
          <bold>false</bold>
         </font>
        </property>
        <property name="text">
         <string>The text appears under the icon (recommended for beginners).</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
