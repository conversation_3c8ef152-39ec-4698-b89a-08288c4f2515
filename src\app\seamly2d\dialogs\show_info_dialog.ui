<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ShowInfoDialog</class>
 <widget class="QDialog" name="ShowInfoDialog">
  <property name="windowModality">
   <enum>Qt::WindowModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>640</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>600</width>
    <height>640</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>600</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="font">
   <font>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Document Information</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/logos/seamly_logo_32.png</normaloff>:/icon/logos/seamly_logo_32.png</iconset>
  </property>
  <property name="sizeGripEnabled">
   <bool>false</bool>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_4">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QToolButton" name="Icon">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>128</width>
           <height>94</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>128</width>
           <height>128</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">border: none;background-color:transparent;</string>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/show_info_icon.png</normaloff>:/icon/show_info_icon.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>128</width>
           <height>94</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QToolButton" name="toolButton">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>128</width>
           <height>31</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>128</width>
           <height>31</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">border: none;background-color:transparent;</string>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/BuiltWithQt.png</normaloff>:/icon/BuiltWithQt.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>128</width>
           <height>31</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QFrame" name="frame">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>50</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(255, 255, 255);</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::Box</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QToolButton" name="clipboard_ToolButton">
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="toolTip">
               <string>Copy info to clipboard</string>
              </property>
              <property name="autoFillBackground">
               <bool>false</bool>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                <normaloff>:/icon/32x32/clipboard_icon.png</normaloff>:/icon/32x32/clipboard_icon.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QToolButton" name="pdf_ToolButton">
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="toolTip">
               <string>Export info as PDF</string>
              </property>
              <property name="text">
               <string notr="true">...</string>
              </property>
              <property name="icon">
               <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                <normaloff>:/icon/32x32/pdf_icon.png</normaloff>:/icon/32x32/pdf_icon.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QToolButton" name="printer_ToolButton">
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="toolTip">
               <string>Send info to the Printer</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                <normaloff>:/icon/32x32/printer_icon.png</normaloff>:/icon/32x32/printer_icon.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QTextBrowser" name="info_TextBrowser">
         <property name="minimumSize">
          <size>
           <width>440</width>
           <height>50</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Close</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>clicked(QAbstractButton*)</signal>
   <receiver>ShowInfoDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>227</x>
     <y>349</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
