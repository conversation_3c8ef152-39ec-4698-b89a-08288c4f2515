<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   version="1.1"
   width="32"
   height="32"
   id="svg3837"
   inkscape:version="0.92.4 (5da689c313, 2019-01-14)"
   sodipodi:docname="group.svg"
   inkscape:export-filename="E:\GitHub\Seamly2D\src\app\seamly2d\share\resources\toolicon\32x32\<EMAIL>"
   inkscape:export-xdpi="244.52852"
   inkscape:export-ydpi="244.52852">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1366"
     inkscape:window-height="745"
     id="namedview12"
     showgrid="false"
     inkscape:zoom="14.75"
     inkscape:cx="2.7191695"
     inkscape:cy="14.554993"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg3837"
     inkscape:snap-global="false" />
  <defs
     id="defs3839" />
  <metadata
     id="metadata3842">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     id="g4686">
    <path
       inkscape:connector-curvature="0"
       id="path940"
       d="M 5.4053833,5.3440926 26.594617,5.2482137"
       style="fill:none;stroke:#000000;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:1, 1;stroke-dashoffset:0;stroke-opacity:1" />
    <path
       inkscape:connector-curvature="0"
       id="path940-6"
       d="M 26.751788,26.594616 26.655908,5.4053838"
       style="fill:none;stroke:#000000;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:1, 1;stroke-dashoffset:0;stroke-opacity:1" />
    <path
       inkscape:connector-curvature="0"
       id="path940-1"
       d="M 5.344092,26.594616 5.248212,5.4053839"
       style="fill:none;stroke:#000000;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:1, 1;stroke-dashoffset:0;stroke-opacity:1" />
    <path
       inkscape:connector-curvature="0"
       id="path940-2"
       d="M 5.4053834,26.751788 26.594617,26.655908"
       style="fill:none;stroke:#000000;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:1, 1;stroke-dashoffset:0;stroke-opacity:1" />
  </g>
  <rect
     y="3.4370494"
     x="3.4370482"
     height="3.7182076"
     width="3.7182076"
     id="rect57"
     style="fill:#ff0000;fill-opacity:1;stroke:#3465a4;stroke-width:0;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     inkscape:export-filename="E:\GitHub\Seamly2D\src\app\seamly2d\share\resources\toolicon\32x32\group.png"
     inkscape:export-xdpi="122.26426"
     inkscape:export-ydpi="122.26426" />
  <rect
     y="24.844744"
     x="24.844744"
     height="3.7182076"
     width="3.7182076"
     id="rect57-6"
     style="fill:#ff0000;fill-opacity:1;stroke:#3465a4;stroke-width:0;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     inkscape:export-filename="E:\GitHub\Seamly2D\src\app\seamly2d\share\resources\toolicon\32x32\group.png"
     inkscape:export-xdpi="122.26426"
     inkscape:export-ydpi="122.26426" />
  <rect
     y="3.4370494"
     x="24.844744"
     height="3.7182076"
     width="3.7182076"
     id="rect57-7"
     style="fill:#ff0000;fill-opacity:1;stroke:#3465a4;stroke-width:0;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     inkscape:export-filename="E:\GitHub\Seamly2D\src\app\seamly2d\share\resources\toolicon\32x32\group.png"
     inkscape:export-xdpi="122.26426"
     inkscape:export-ydpi="122.26426" />
  <rect
     y="24.844744"
     x="3.4370482"
     height="3.7182076"
     width="3.7182076"
     id="rect57-5"
     style="fill:#ff0000;fill-opacity:1;stroke:#3465a4;stroke-width:0;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
     inkscape:export-filename="E:\GitHub\Seamly2D\src\app\seamly2d\share\resources\toolicon\32x32\group.png"
     inkscape:export-xdpi="122.26426"
     inkscape:export-ydpi="122.26426" />
  <rect
     style="fill:#000000;fill-opacity:0;stroke:#000000;stroke-width:0.5;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:0"
     id="rect830"
     width="9"
     height="9"
     x="14.508474"
     y="14.644068" />
  <g
     id="g825"
     transform="translate(-0.5095569,-0.48514285)">
    <path
       id="rect40"
       d="m 8.6894531,8.640625 v 9.300781 h 5.3203129 v -3.882812 h 3.980468 V 8.640625 Z"
       style="fill:#000000;fill-opacity:1;stroke:#3465a4;stroke-width:0;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
       inkscape:connector-curvature="0" />
    <rect
       y="14.779661"
       x="14.779661"
       height="9"
       width="9"
       id="rect832"
       style="fill:#000000;fill-opacity:1;stroke:#000000;stroke-width:1.10000002;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:0" />
  </g>
</svg>
