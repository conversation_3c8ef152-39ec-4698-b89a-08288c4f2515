<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PreferencesConfigurationPage</class>
 <widget class="QWidget" name="PreferencesConfigurationPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>472</width>
    <height>572</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>100</width>
    <height>100</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string notr="true">Configuration</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_13">
   <item>
    <widget class="QTabWidget" name="company_Tab">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="autoFillBackground">
      <bool>false</bool>
     </property>
     <property name="tabPosition">
      <enum>QTabWidget::West</enum>
     </property>
     <property name="currentIndex">
      <number>3</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>Designer Info</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_14">
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Company / Designer Info</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_12">
          <item>
           <layout class="QFormLayout" name="formLayout">
            <item row="0" column="0">
             <widget class="QLabel" name="company_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Company / Designer:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="companyName_LineEdit">
              <property name="autoFillBackground">
               <bool>false</bool>
              </property>
              <property name="frame">
               <bool>true</bool>
              </property>
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="contact_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Contact:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="contact_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="address_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Address:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QLineEdit" name="address_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="city_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>City:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="QLineEdit" name="city_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="state_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>State:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QLineEdit" name="state_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="5" column="0">
             <widget class="QLabel" name="zipcode_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Zipcode:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="5" column="1">
             <widget class="QLineEdit" name="zipcode_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="6" column="0">
             <widget class="QLabel" name="country_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Country:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="6" column="1">
             <widget class="QLineEdit" name="country_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="7" column="0">
             <widget class="QLabel" name="telephone_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Telephone:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="7" column="1">
             <widget class="QLineEdit" name="telephone_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="8" column="0">
             <widget class="QLabel" name="fax_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Fax:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="8" column="1">
             <widget class="QLineEdit" name="fax_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="9" column="0">
             <widget class="QLabel" name="email_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Email:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="9" column="1">
             <widget class="QLineEdit" name="email_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item row="10" column="0">
             <widget class="QLabel" name="website_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Website:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="10" column="1">
             <widget class="QLineEdit" name="website_LineEdit">
              <property name="clearButtonEnabled">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer_4">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>177</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="editing_Tab">
      <attribute name="title">
       <string>Editing</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_9">
       <item>
        <widget class="QGroupBox" name="undo_GroupBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Undo</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="topMargin">
           <number>6</number>
          </property>
          <property name="bottomMargin">
           <number>6</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <item>
             <widget class="QLabel" name="undoCount_Label">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Count step:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QSpinBox" name="undoCount_SpinBox">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label_10">
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string> (0 - no limit)</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_5">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="selectionSound_GroupBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Selection sound</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_11">
          <property name="topMargin">
           <number>6</number>
          </property>
          <property name="bottomMargin">
           <number>6</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_8">
            <item>
             <widget class="QLabel" name="selectionSound__Label">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Sound:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="selectionSound_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="locale">
               <locale language="English" country="UnitedStates"/>
              </property>
              <property name="currentText">
               <string notr="true">silent</string>
              </property>
              <property name="placeholderText">
               <string notr="true"/>
              </property>
              <item>
               <property name="text">
                <string notr="true">silent</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">button_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">mouse1_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">mouse2_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">mouse3_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">camera_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">camera_buzz</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">clock_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">clock_beep</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">microwave_beep</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">coffeemaker_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">coffeemaker2_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">inspirstar_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">phone2_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">phone1_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">scissors_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">wallswitch_click</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">buzz_click</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="patternEditing_GroupBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Pattern Editing Warnings</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_7">
          <property name="topMargin">
           <number>6</number>
          </property>
          <property name="bottomMargin">
           <number>6</number>
          </property>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_8">
            <item>
             <widget class="QCheckBox" name="confirmItemDelete_CheckBox">
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Confirm Item Delete</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="confirmFormatRewriting_CheckBox">
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Confirm Format Rewriting</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="suffix_GroupBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Operations Default Suffix</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="topMargin">
           <number>6</number>
          </property>
          <property name="bottomMargin">
           <number>6</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <item>
             <widget class="QLabel" name="label_11">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Mirror by axis suffix:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="mirrorByAxisSuffix_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_7">
            <item>
             <widget class="QLabel" name="label_12">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Mirror by line suffix:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="mirrorByLineSuffix_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QLabel" name="label_13">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Move suffix:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="moveSuffix_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QLabel" name="label_14">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Rotate suffix:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="rotateSuffix_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="fileHandling_Tab">
      <attribute name="title">
       <string>File Handling</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_6">
       <item>
        <widget class="QGroupBox" name="backup_Groupbox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Backups</string>
         </property>
         <property name="checkable">
          <bool>false</bool>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout">
          <item>
           <widget class="QCheckBox" name="convertBackupEnabled_CheckBox">
            <property name="text">
             <string>Create backup file when converting</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="autoSave_CheckBox">
            <property name="text">
             <string>Enable Autosave</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <widget class="QLabel" name="interval_Label">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
                <bold>false</bold>
               </font>
              </property>
              <property name="text">
               <string>Interval:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QSpinBox" name="autoInterval_Spinbox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
                <bold>false</bold>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
              <property name="suffix">
               <string> min</string>
              </property>
              <property name="prefix">
               <string>Every </string>
              </property>
              <property name="minimum">
               <number>1</number>
              </property>
              <property name="maximum">
               <number>60</number>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_6">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="exportFormat_GroupBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Export Format</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <item>
           <widget class="QCheckBox" name="useModeType_CheckBox">
            <property name="text">
             <string>Include mode type in filename</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="uselastExportFormat_CheckBox">
            <property name="text">
             <string>Save last used</string>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="QLabel" name="defaultExportFormat_Label">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Default:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="ExportFormatCombobox" name="defaultExportFormat_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>240</width>
                <height>0</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>353</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="language_Tab">
      <attribute name="title">
       <string>Startup</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_10">
       <item>
        <widget class="QGroupBox" name="startup_GroupBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>10</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Welcome</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_15">
          <item>
           <widget class="QCheckBox" name="showWelcome_CheckBox">
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Do not show welcome screen</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="language_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>10</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Language</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_11">
            <item>
             <widget class="QLabel" name="label_6">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>GUI language:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="langCombo">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>240</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_12">
            <item>
             <widget class="QLabel" name="label_7">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Decimal separator:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="osOptionCheck">
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string notr="true">&lt; User locale &gt;</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_13">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_13">
            <item>
             <widget class="QLabel" name="label_8">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Default unit:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="unitCombo">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>240</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_14">
            <item>
             <widget class="QLabel" name="label_9">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Point name text:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="labelCombo">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>240</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ExportFormatCombobox</class>
   <extends>QComboBox</extends>
   <header>export_format_combobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
