"""
T恤版型生成模块 - 升级版
包含前片、后片和袖子的完整T恤版型
"""

import os
import logging
from typing import Dict, Optional, Tuple, List
import matplotlib.pyplot as plt
import numpy as np
import math

from ..core.pattern_generator import Seamly2DPatternGenerator
from ..core.config import PatternConfig
from ..core.exceptions import PatternGenerationError, MeasurementError

logger = logging.getLogger(__name__)

class TShirt:
    """完整T恤版型生成器 - 包含前片、后片和袖子"""
    
    def __init__(self, config: Optional[PatternConfig] = None):
        """初始化T恤版型生成器
        
        Args:
            config: 版型配置对象，如果为None则使用默认配置
        """
        self.config = config or PatternConfig()
        self.generator = Seamly2DPatternGenerator(self.config)
        self.measurements = {}
        self.ease = self.config.default_ease
        self.name = "完整T恤版型"
        self.description = "包含前片、后片和袖子的完整T恤版型"
        
        # T恤特有参数
        self.neck_depth_front = 7.0  # 前领深
        self.neck_depth_back = 2.0   # 后领深
        self.sleeve_length = 18.0    # 袖长
        self.armhole_depth = 20.0    # 袖窿深
        
    def set_measurements(self, measurements: Dict[str, float]) -> 'TShirt':
        """设置测量数据
        
        Args:
            measurements: 测量数据字典
            
        Returns:
            self: 支持方法链式调用
        """
        self.measurements = measurements
        self.generator.set_measurements(measurements)
        return self
    
    def set_ease(self, ease: float) -> 'TShirt':
        """设置放松量
        
        Args:
            ease: 放松量(cm)
            
        Returns:
            self: 支持方法链式调用
        """
        self.ease = ease
        return self
    
    def set_neck_depth(self, front: float, back: float) -> 'TShirt':
        """设置领深
        
        Args:
            front: 前领深(cm)
            back: 后领深(cm)
            
        Returns:
            self: 支持方法链式调用
        """
        self.neck_depth_front = front
        self.neck_depth_back = back
        return self
    
    def set_sleeve_length(self, length: float) -> 'TShirt':
        """设置袖长
        
        Args:
            length: 袖长(cm)
            
        Returns:
            self: 支持方法链式调用
        """
        self.sleeve_length = length
        return self
    
    def set_armhole_depth(self, depth: float) -> 'TShirt':
        """设置袖窿深
        
        Args:
            depth: 袖窿深(cm)
            
        Returns:
            self: 支持方法链式调用
        """
        self.armhole_depth = depth
        return self
    
    def create_pattern(self, output_file: str = "complete_tshirt_pattern.sm2d", 
                      preview: bool = True) -> str:
        """创建完整T恤版型
        
        Args:
            output_file: 输出文件路径
            preview: 是否生成预览图
            
        Returns:
            str: 版型文件路径
        """
        # 检查必要的测量数据
        required = ["chest_width", "shoulder_width", "length"]
        for field in required:
            if field not in self.measurements:
                raise MeasurementError(f"缺少必要的测量数据: {field}")
        
        # 提取关键尺寸
        chest_width = self.measurements["chest_width"]
        shoulder_width = self.measurements["shoulder_width"]
        length = self.measurements["length"]
        neck_width = self.measurements.get("neck_width", chest_width/6)
        
        # 应用放松量
        chest_with_ease = chest_width + self.ease
        
        # 创建新版型
        self.generator.create_new_pattern(self.name, self.description)
        
        # 添加T恤特有的增量变量
        self.generator.add_increment("#neckDepthFront", str(self.neck_depth_front), "前领深")
        self.generator.add_increment("#neckDepthBack", str(self.neck_depth_back), "后领深")
        self.generator.add_increment("#sleeveLength", str(self.sleeve_length), "袖长")
        self.generator.add_increment("#armholeDepth", str(self.armhole_depth), "袖窿深")
        self.generator.add_increment("#chestWithEase", f"#{chest_width}+{self.ease}", "胸围加放松量")
        
        # 创建各个部件
        self._create_front_piece()
        self._create_back_piece()
        self._create_sleeve()
        
        # 保存版型
        pattern_file = self.generator.save_pattern(output_file)
        
        # 生成预览图
        if preview:
            preview_file = output_file.replace(".sm2d", "_preview.png")
            self.generate_preview(preview_file)
        
        return pattern_file
    
    def _create_front_piece(self) -> None:
        """创建前片"""
        # 前片基本点
        p1 = self.generator.add_point("0", "0", "A_前肩左", "base")  # 左肩点
        p2 = self.generator.add_point("#shoulder_width", "0", "B_前肩右")  # 右肩点
        p3 = self.generator.add_point("0", "#length", "C_前下摆左")  # 左下摆点
        p4 = self.generator.add_point("#chestWithEase", "#length", "D_前下摆右")  # 右下摆点
        
        # 袖窿点
        p5 = self.generator.add_point("0", "#armholeDepth", "E_前袖窿左")  # 左袖窿点
        p6 = self.generator.add_point("#chestWithEase", "#armholeDepth", "F_前袖窿右")  # 右袖窿点
        
        # 领口点
        p7 = self.generator.add_point("#shoulder_width/4", "0", "G_前领右")  # 前领右点
        p8 = self.generator.add_point("0", "#neckDepthFront", "H_前领底")  # 前领底点
        
        # 添加线条
        self.generator.add_line(p8, p5, "前左侧上")
        self.generator.add_line(p5, p3, "前左侧下")
        self.generator.add_line(p3, p4, "前下摆")
        self.generator.add_line(p4, p6, "前右侧下")
        self.generator.add_line(p6, p2, "前右侧上")
        self.generator.add_line(p2, p7, "前右肩")
        self.generator.add_line(p1, p7, "前左肩")
        self.generator.add_line(p7, p8, "前领口")
        self.generator.add_line(p1, p5, "前左袖窿")
        self.generator.add_line(p2, p6, "前右袖窿")
        
        # 创建前片
        self.generator.create_piece("前片", [p1, p7, p8, p5, p3, p4, p6, p2], True)
    
    def _create_back_piece(self) -> None:
        """创建后片"""
        # 后片基本点 (X坐标偏移以避免重叠)
        offset_x = "#chestWithEase + 10"  # 偏移10cm
        
        p1 = self.generator.add_point(offset_x, "0", "A_后肩左")  # 左肩点
        p2 = self.generator.add_point(f"{offset_x} + #shoulder_width", "0", "B_后肩右")  # 右肩点
        p3 = self.generator.add_point(offset_x, "#length", "C_后下摆左")  # 左下摆点
        p4 = self.generator.add_point(f"{offset_x} + #chestWithEase", "#length", "D_后下摆右")  # 右下摆点
        
        # 袖窿点
        p5 = self.generator.add_point(offset_x, "#armholeDepth", "E_后袖窿左")  # 左袖窿点
        p6 = self.generator.add_point(f"{offset_x} + #chestWithEase", "#armholeDepth", "F_后袖窿右")  # 右袖窿点
        
        # 领口点 (后领深较浅)
        p7 = self.generator.add_point(f"{offset_x} + #shoulder_width/4", "0", "G_后领右")  # 后领右点
        p8 = self.generator.add_point(offset_x, "#neckDepthBack", "H_后领底")  # 后领底点
        
        # 添加线条
        self.generator.add_line(p8, p5, "后左侧上")
        self.generator.add_line(p5, p3, "后左侧下")
        self.generator.add_line(p3, p4, "后下摆")
        self.generator.add_line(p4, p6, "后右侧下")
        self.generator.add_line(p6, p2, "后右侧上")
        self.generator.add_line(p2, p7, "后右肩")
        self.generator.add_line(p1, p7, "后左肩")
        self.generator.add_line(p7, p8, "后领口")
        self.generator.add_line(p1, p5, "后左袖窿")
        self.generator.add_line(p2, p6, "后右袖窿")
        
        # 创建后片
        self.generator.create_piece("后片", [p1, p7, p8, p5, p3, p4, p6, p2], False)
    
    def _create_sleeve(self) -> None:
        """创建袖子"""
        # 袖子基本点 (Y坐标偏移以避免重叠)
        offset_y = "#length + 15"  # 偏移15cm
        sleeve_width = "#armholeDepth * 1.3"  # 袖宽为袖窿深的1.3倍
        
        p1 = self.generator.add_point("0", offset_y, "A_袖山左")  # 袖山左点
        p2 = self.generator.add_point(sleeve_width, offset_y, "B_袖山右")  # 袖山右点
        p3 = self.generator.add_point("0", f"{offset_y} + #sleeveLength", "C_袖口左")  # 袖口左点
        p4 = self.generator.add_point(sleeve_width, f"{offset_y} + #sleeveLength", "D_袖口右")  # 袖口右点
        
        # 袖山顶点
        p5 = self.generator.add_point(f"{sleeve_width}/2", f"{offset_y} - 8", "E_袖山顶")  # 袖山顶点
        
        # 添加线条
        self.generator.add_line(p1, p5, "袖山左")
        self.generator.add_line(p5, p2, "袖山右")
        self.generator.add_line(p2, p4, "袖右侧")
        self.generator.add_line(p4, p3, "袖口")
        self.generator.add_line(p3, p1, "袖左侧")
        
        # 创建袖子
        self.generator.create_piece("袖子", [p1, p5, p2, p4, p3], False)
    
    def generate_preview(self, output_file: str = "complete_tshirt_preview.png") -> str:
        """生成完整T恤预览图
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            str: 输出文件的绝对路径
        """
        fig, ax = plt.subplots(figsize=(16, 12))
        
        # 提取关键尺寸
        chest_width = self.measurements.get("chest_width", 47.0)
        shoulder_width = self.measurements.get("shoulder_width", 12.4)
        length = self.measurements.get("length", 61.0)
        
        # 应用放松量
        chest_with_ease = chest_width + self.ease
        
        # 绘制前片
        self._draw_front_piece(ax, chest_with_ease, shoulder_width, length)
        
        # 绘制后片
        self._draw_back_piece(ax, chest_with_ease, shoulder_width, length)
        
        # 绘制袖子
        self._draw_sleeve(ax, length)
        
        # 设置图表
        ax.set_aspect('equal')
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.set_title('完整T恤版型预览', fontsize=16, fontweight='bold')
        ax.set_xlabel('宽度 (cm)', fontsize=12)
        ax.set_ylabel('长度 (cm)', fontsize=12)
        
        # 设置坐标范围
        total_width = chest_with_ease * 2 + 20
        total_height = length + self.sleeve_length + 25
        ax.set_xlim(-5, total_width)
        ax.set_ylim(-15, total_height)
        
        # 反转Y轴以匹配Seamly2D坐标系
        ax.invert_yaxis()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        logger.info(f"完整预览图已保存到: {output_file}")
        return os.path.abspath(output_file)
    
    def _draw_front_piece(self, ax, chest_with_ease, shoulder_width, length):
        """绘制前片"""
        # 前片轮廓点
        front_points = [
            (0, 0),  # A_前肩左
            (shoulder_width/4, 0),  # G_前领右
            (0, self.neck_depth_front),  # H_前领底
            (0, self.armhole_depth),  # E_前袖窿左
            (0, length),  # C_前下摆左
            (chest_with_ease, length),  # D_前下摆右
            (chest_with_ease, self.armhole_depth),  # F_前袖窿右
            (shoulder_width, 0),  # B_前肩右
        ]
        
        # 绘制轮廓
        for i in range(len(front_points)):
            p1 = front_points[i]
            p2 = front_points[(i + 1) % len(front_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'b-', linewidth=2)
        
        # 添加标注
        ax.text(chest_with_ease/2, -3, f"前片 - 胸围: {chest_with_ease}cm", 
               ha='center', fontsize=10, fontweight='bold', color='blue')
    
    def _draw_back_piece(self, ax, chest_with_ease, shoulder_width, length):
        """绘制后片"""
        offset_x = chest_with_ease + 10
        
        # 后片轮廓点
        back_points = [
            (offset_x, 0),  # A_后肩左
            (offset_x + shoulder_width/4, 0),  # G_后领右
            (offset_x, self.neck_depth_back),  # H_后领底
            (offset_x, self.armhole_depth),  # E_后袖窿左
            (offset_x, length),  # C_后下摆左
            (offset_x + chest_with_ease, length),  # D_后下摆右
            (offset_x + chest_with_ease, self.armhole_depth),  # F_后袖窿右
            (offset_x + shoulder_width, 0),  # B_后肩右
        ]
        
        # 绘制轮廓
        for i in range(len(back_points)):
            p1 = back_points[i]
            p2 = back_points[(i + 1) % len(back_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'g-', linewidth=2)
        
        # 添加标注
        ax.text(offset_x + chest_with_ease/2, -3, f"后片 - 胸围: {chest_with_ease}cm", 
               ha='center', fontsize=10, fontweight='bold', color='green')
    
    def _draw_sleeve(self, ax, length):
        """绘制袖子"""
        offset_y = length + 15
        sleeve_width = self.armhole_depth * 1.3
        
        # 袖子轮廓点
        sleeve_points = [
            (0, offset_y),  # A_袖山左
            (sleeve_width/2, offset_y - 8),  # E_袖山顶
            (sleeve_width, offset_y),  # B_袖山右
            (sleeve_width, offset_y + self.sleeve_length),  # D_袖口右
            (0, offset_y + self.sleeve_length),  # C_袖口左
        ]
        
        # 绘制轮廓
        for i in range(len(sleeve_points)):
            p1 = sleeve_points[i]
            p2 = sleeve_points[(i + 1) % len(sleeve_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'r-', linewidth=2)
        
        # 添加标注
        ax.text(sleeve_width/2, offset_y + self.sleeve_length + 2, 
               f"袖子 - 袖长: {self.sleeve_length}cm", 
               ha='center', fontsize=10, fontweight='bold', color='red') 