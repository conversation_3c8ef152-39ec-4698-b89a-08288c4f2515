<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PreferencesPatternPage</class>
 <widget class="QWidget" name="PreferencesPatternPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>485</width>
    <height>570</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>100</width>
    <height>100</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string notr="true">Pattern</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_25">
   <item>
    <widget class="QTabWidget" name="tabWidget_2">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>465</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="tabPosition">
      <enum>QTabWidget::West</enum>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab_4">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Pattern Piece</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_15">
       <item>
        <widget class="QGroupBox" name="WorkPiece_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>420</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Properties</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_9">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_7">
            <item>
             <widget class="QCheckBox" name="showSeamAllowances_CheckBox">
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Show Cut Line</string>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="hideSeamLine_CheckBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="toolTip">
               <string>By default hide the main path if the seam allowance was enabled</string>
              </property>
              <property name="text">
               <string>Hide Seam Line</string>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="forbidFlipping_CheckBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="toolTip">
               <string>By default forbid flipping for all new created workpieces</string>
              </property>
              <property name="text">
               <string>Forbid flipping</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>440</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_5">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Notches</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QGroupBox" name="notchPorperties_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>420</width>
           <height>80</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Properties</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_6">
          <item>
           <widget class="QCheckBox" name="showSeamAllowanceNotch_CheckBox">
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Show notch on Cut Line</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="showSeamlineNotch_CheckBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="toolTip">
             <string>Show notch on both the seam allowance and seam line.</string>
            </property>
            <property name="text">
             <string>Show notch on Seam Line</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="Attribute_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>150</height>
          </size>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Attributes</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_14">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <item>
               <widget class="QLabel" name="defaultNotchType_Label">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Type:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="defaultNotchType_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>120</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
                <property name="currentIndex">
                 <number>-1</number>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_6">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>147</width>
                  <height>13</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_6">
              <item>
               <widget class="QLabel" name="notchColor_Label">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Color:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="ColorComboBox" name="defaultNotchColor_ComboBox">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font/>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_8">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>160</width>
                  <height>15</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_5">
              <item>
               <widget class="QLabel" name="defaultNotchLength_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Length:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QDoubleSpinBox" name="defaultNotchLength_DoubleSpinBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>120</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
                <property name="decimals">
                 <number>3</number>
                </property>
                <property name="minimum">
                 <double>0.125000000000000</double>
                </property>
                <property name="maximum">
                 <double>1.500000000000000</double>
                </property>
                <property name="singleStep">
                 <double>0.010000000000000</double>
                </property>
                <property name="value">
                 <double>0.125000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_2">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>160</width>
                  <height>15</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_7">
              <item>
               <widget class="QLabel" name="defaultNotchWidth_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Width:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QDoubleSpinBox" name="defaultNotchWidth_DoubleSpinBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>120</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
                <property name="decimals">
                 <number>3</number>
                </property>
                <property name="singleStep">
                 <double>0.050000000000000</double>
                </property>
                <property name="value">
                 <double>0.250000000000000</double>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_7">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>160</width>
                  <height>15</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_5">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>354</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_10">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Grainlines</string>
      </attribute>
      <widget class="QGroupBox" name="grainlineProperties_GroupBox">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>420</width>
         <height>180</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>420</width>
         <height>180</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="title">
        <string>Properties</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_20">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_21">
          <item>
           <widget class="QCheckBox" name="showGrainlines_CheckBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="toolTip">
             <string>By default forbid flipping for all new created workpieces</string>
            </property>
            <property name="text">
             <string>Show grainlines</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_8">
          <item>
           <widget class="QLabel" name="grainlineLength_Label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Length:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="defaultGrainlineLength_DoubleSpinBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>20</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
            <property name="decimals">
             <number>3</number>
            </property>
            <property name="minimum">
             <double>1.000000000000000</double>
            </property>
            <property name="maximum">
             <double>20.000000000000000</double>
            </property>
            <property name="singleStep">
             <double>0.100000000000000</double>
            </property>
            <property name="value">
             <double>2.000000000000000</double>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_24">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>58</width>
              <height>17</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_21">
          <item>
           <widget class="QLabel" name="grainlineColor_Label">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Color:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="ColorComboBox" name="defaultGrainlineColor_ComboBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>20</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_21">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>160</width>
              <height>15</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_22">
          <item>
           <widget class="QLabel" name="grainlinelLineweight_Label">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Lineweight:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="LineWeightComboBox" name="defaultGrainlineLineweight_ComboBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>20</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>x 3</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_22">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_26">
          <item>
           <widget class="QLabel" name="defaultArrowLength_Label">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Arrow length:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="defaultArrowLength_DoubleSpinBox">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>20</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
            <property name="suffix">
             <string> px</string>
            </property>
            <property name="decimals">
             <number>0</number>
            </property>
            <property name="minimum">
             <double>25.000000000000000</double>
            </property>
            <property name="maximum">
             <double>200.000000000000000</double>
            </property>
            <property name="value">
             <double>70.000000000000000</double>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QWidget" name="tab_6">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Paths</string>
      </attribute>
      <widget class="QGroupBox" name="seamAllowance_GroupBox">
       <property name="geometry">
        <rect>
         <x>9</x>
         <y>9</y>
         <width>420</width>
         <height>60</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>420</width>
         <height>60</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="font">
        <font>
         <pointsize>9</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="title">
        <string>Seam allowance</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_23">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QLabel" name="defaultSeamAllowance_Label">
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Default value:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="defaultSeamAllowance_DoubleSpinBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>120</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
            <property name="decimals">
             <number>3</number>
            </property>
            <property name="maximum">
             <double>100.000000000000000</double>
            </property>
            <property name="singleStep">
             <double>0.100000000000000</double>
            </property>
            <property name="value">
             <double>1.000000000000000</double>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QGroupBox" name="groupBox">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>80</y>
         <width>421</width>
         <height>160</height>
        </rect>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>160</height>
        </size>
       </property>
       <property name="font">
        <font>
         <bold>true</bold>
        </font>
       </property>
       <property name="title">
        <string>Paths</string>
       </property>
       <widget class="QTabWidget" name="tabWidget">
        <property name="geometry">
         <rect>
          <x>0</x>
          <y>20</y>
          <width>411</width>
          <height>126</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>100</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>140</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>9</pointsize>
         </font>
        </property>
        <property name="currentIndex">
         <number>0</number>
        </property>
        <widget class="QWidget" name="tab">
         <attribute name="title">
          <string>Seam Line</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_11">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_10">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_9">
              <item>
               <widget class="QLabel" name="seamColor_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>18</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Color:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="ColorComboBox" name="defaultSeamColor_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_9">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_10">
              <item>
               <widget class="QLabel" name="seamlLinetype_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>18</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Linetype:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="LineTypeComboBox" name="defaultSeamLinetype_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_10">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_14">
              <item>
               <widget class="QLabel" name="seamlLineweight_Label">
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>65</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Lineweight</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="LineWeightComboBox" name="defaultSeamLineweight_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_14">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab_2">
         <attribute name="title">
          <string>Cut Line</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_12">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_11">
              <item>
               <widget class="QLabel" name="cutColor_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>18</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Color:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="ColorComboBox" name="defaultCutColor_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_11">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_12">
              <item>
               <widget class="QLabel" name="cutLineType_Label">
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>18</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Linetype:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="LineTypeComboBox" name="defaultCutLinetype_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_12">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_13">
              <item>
               <widget class="QLabel" name="cutLineWeight_Labe">
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>65</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Lineweight</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="LineWeightComboBox" name="defaultCutLineweight_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_13">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab_8">
         <attribute name="title">
          <string>Internals</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_18">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_13">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_15">
              <item>
               <widget class="QLabel" name="internalColor_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>18</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Color:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="ColorComboBox" name="defaultInternalColor_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_15">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_16">
              <item>
               <widget class="QLabel" name="internalLinetype_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>18</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Linetype:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="LineTypeComboBox" name="defaultInternalLinetype_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_16">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_17">
              <item>
               <widget class="QLabel" name="internalLineweight_Label">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>65</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Lineweight</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="LineWeightComboBox" name="defaultInternalLineweight_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_17">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tab_9">
         <attribute name="title">
          <string>Cutouts</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_19">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_16">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_18">
              <item>
               <widget class="QLabel" name="cutoutColor_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>18</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Color:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="ColorComboBox" name="defaultCutoutColor_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_18">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_19">
              <item>
               <widget class="QLabel" name="cutoutLinetype_Label">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>18</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Linetype:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="LineTypeComboBox" name="defaultCutoutLinetype_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_19">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_20">
              <item>
               <widget class="QLabel" name="cutoutlLineweight_Label">
                <property name="minimumSize">
                 <size>
                  <width>65</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>65</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Lineweight</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="LineWeightComboBox" name="defaultCutoutLineweight_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_20">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </widget>
      </widget>
     </widget>
     <widget class="QWidget" name="tab_7">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <attribute name="title">
       <string>Labels</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_17">
       <item>
        <widget class="QGroupBox" name="labelsProperties_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>420</width>
           <height>170</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
          </font>
         </property>
         <property name="title">
          <string>Properties</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_22">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_24">
            <item>
             <widget class="QCheckBox" name="showPatternLabels_CheckBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="toolTip">
               <string>By default forbid flipping for all new created workpieces</string>
              </property>
              <property name="text">
               <string>Show pattern labels</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="showPieceLabels_CheckBox">
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Show piece labels</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_24">
            <item>
             <widget class="QLabel" name="labelWidth_Label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Width</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="defaultLabelWidth_DoubleSpinBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
              <property name="decimals">
               <number>3</number>
              </property>
              <property name="minimum">
               <double>1.000000000000000</double>
              </property>
              <property name="maximum">
               <double>20.000000000000000</double>
              </property>
              <property name="singleStep">
               <double>0.100000000000000</double>
              </property>
              <property name="value">
               <double>3.000000000000000</double>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_25">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>58</width>
                <height>17</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_25">
            <item>
             <widget class="QLabel" name="labelHeight_Label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Height</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="defaultLabelHeight_DoubleSpinBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
              <property name="decimals">
               <number>3</number>
              </property>
              <property name="minimum">
               <double>1.000000000000000</double>
              </property>
              <property name="maximum">
               <double>20.000000000000000</double>
              </property>
              <property name="singleStep">
               <double>0.100000000000000</double>
              </property>
              <property name="value">
               <double>2.000000000000000</double>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_26">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>58</width>
                <height>17</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_23">
            <item>
             <widget class="QLabel" name="labelsColor_Label">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Color:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="ColorComboBox" name="defaultLabelColor_ComboBox">
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>20</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_23">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>160</width>
                <height>15</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="Formats_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>420</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
          </font>
         </property>
         <property name="title">
          <string>Label data (date/time format)</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <item>
               <widget class="QLabel" name="dateFormats_Label">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Date:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="dateFormats_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>120</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="editDateFormats_PushButton">
                <property name="minimumSize">
                 <size>
                  <width>75</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Edit formats</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_4">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>37</width>
                  <height>17</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <item>
               <widget class="QLabel" name="timeFormats_Label">
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Time:</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QComboBox" name="timeFormats_ComboBox">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>120</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>120</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true"/>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QPushButton" name="editTimeFormats_PushButton">
                <property name="minimumSize">
                 <size>
                  <width>75</width>
                  <height>20</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>Edit formats</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_5">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_2">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>100</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
          </font>
         </property>
         <property name="title">
          <string>Templates</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_8">
          <item>
           <layout class="QGridLayout" name="gridLayout">
            <item row="0" column="0">
             <widget class="QLabel" name="patternTemplate_Label">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Pattern label:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="patternTemplate_LineEdit">
              <property name="styleSheet">
               <string notr="true"/>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <widget class="QToolButton" name="patternTemplate_ToolButton">
              <property name="text">
               <string notr="true">...</string>
              </property>
              <property name="icon">
               <iconset resource="../../../../libs/vmisc/share/resources/theme.qrc">
                <normaloff>:/icons/win.icon.theme/24x24/actions/document-open.png</normaloff>:/icons/win.icon.theme/24x24/actions/document-open.png</iconset>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="pieceTemplate_Label">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Piece label:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QLineEdit" name="pieceTemplate_LineEdit">
              <property name="styleSheet">
               <string notr="true"/>
              </property>
             </widget>
            </item>
            <item row="1" column="2">
             <widget class="QToolButton" name="pieceTemplate_ToolButton">
              <property name="text">
               <string notr="true">...</string>
              </property>
              <property name="icon">
               <iconset resource="../../../../libs/vmisc/share/resources/theme.qrc">
                <normaloff>:/icons/win.icon.theme/24x24/actions/document-open.png</normaloff>:/icons/win.icon.theme/24x24/actions/document-open.png</iconset>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>425</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ColorComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">color_combobox.h</header>
  </customwidget>
  <customwidget>
   <class>LineTypeComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">linetype_combobox.h</header>
  </customwidget>
  <customwidget>
   <class>LineWeightComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">lineweight_combobox.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../../libs/vmisc/share/resources/theme.qrc"/>
 </resources>
 <connections/>
</ui>
