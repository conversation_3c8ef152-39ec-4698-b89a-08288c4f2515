"""
专业女式衬衫版型生成模块
基于工业制版图纸的高精度女式衬衫版型生成器
包含精确的曲线设计、专业省道和标准袖山弧度
"""

import os
import logging
from typing import Dict, Optional, Tuple, List
import matplotlib.pyplot as plt
import numpy as np
import math

from ..core.pattern_generator import Seamly2DPatternGenerator
from ..core.config import PatternConfig
from ..core.exceptions import PatternGenerationError, MeasurementError

logger = logging.getLogger(__name__)

class ProfessionalWomensShirt:
    """专业女式衬衫版型生成器 - 基于工业制版标准"""
    
    def __init__(self, config: Optional[PatternConfig] = None):
        """初始化专业女式衬衫版型生成器
        
        Args:
            config: 版型配置对象，如果为None则使用默认配置
        """
        self.config = config or PatternConfig()
        self.generator = Seamly2DPatternGenerator(self.config)
        self.measurements = {}
        self.ease = 6.0  # 专业制版标准放松量
        self.name = "专业女式衬衫版型"
        self.description = "基于工业制版图纸的高精度女式衬衫版型"
        
        # 基于制版图的专业参数
        self.neck_depth_front = 7.4   # 前领深
        self.neck_depth_back = 2.4    # 后领深
        self.sleeve_length = 54.0     # 袖长
        self.armhole_depth = 23.0     # 袖窿深
        self.dart_length = 17.9       # 胸省长度
        self.collar_width = 7.0       # 领宽
        self.collar_curve = 2.5       # 领子弯曲度
        self.button_overlap = 2.5     # 门襟重叠
        
        # 袖子专业参数
        self.sleeve_cap_height = 22.0  # 袖山高
        self.sleeve_width = 33.0       # 袖宽
        self.sleeve_cuff_width = 22.0  # 袖口宽
        
        # 版型比例参数（基于图纸）
        self.body_width = 50.0         # 身宽
        self.body_length = 56.5        # 身长
        self.chest_line = 21.0         # 胸围线位置
        self.waist_line = 37.0         # 腰围线位置
        
    def set_measurements(self, measurements: Dict[str, float]) -> 'ProfessionalWomensShirt':
        """设置测量数据
        
        Args:
            measurements: 测量数据字典
            
        Returns:
            self: 支持方法链式调用
        """
        self.measurements = measurements
        self.generator.set_measurements(measurements)
        return self
    
    def set_ease(self, ease: float) -> 'ProfessionalWomensShirt':
        """设置放松量
        
        Args:
            ease: 放松量(cm)
            
        Returns:
            self: 支持方法链式调用
        """
        self.ease = ease
        return self
    
    def create_pattern(self, output_file: str = "professional_womens_shirt.sm2d", 
                      preview: bool = True) -> str:
        """创建专业女式衬衫版型
        
        Args:
            output_file: 输出文件路径
            preview: 是否生成预览图
            
        Returns:
            str: 版型文件路径
        """
        # 检查必要的测量数据
        required = ["bust", "shoulder_width", "length", "waist"]
        for field in required:
            if field not in self.measurements:
                raise MeasurementError(f"缺少必要的测量数据: {field}")
        
        # 提取关键尺寸
        bust = self.measurements["bust"]
        shoulder_width = self.measurements["shoulder_width"]
        length = self.measurements["length"]
        waist = self.measurements["waist"]
        
        # 按制版图比例计算尺寸
        scale_factor = bust / 88.0  # 以88cm胸围为基准
        
        # 应用比例缩放
        body_width = self.body_width * scale_factor
        body_length = self.body_length * scale_factor
        armhole_depth = self.armhole_depth * scale_factor
        
        # 创建新版型
        self.generator.create_new_pattern(self.name, self.description)
        
        # 添加专业制版变量
        self.generator.add_increment("#bodyWidth", str(body_width), "身宽")
        self.generator.add_increment("#bodyLength", str(body_length), "身长")
        self.generator.add_increment("#armholeDepth", str(armhole_depth), "袖窿深")
        self.generator.add_increment("#chestLine", str(self.chest_line * scale_factor), "胸围线")
        self.generator.add_increment("#waistLine", str(self.waist_line * scale_factor), "腰围线")
        self.generator.add_increment("#neckDepthFront", str(self.neck_depth_front), "前领深")
        self.generator.add_increment("#neckDepthBack", str(self.neck_depth_back), "后领深")
        self.generator.add_increment("#dartLength", str(self.dart_length * scale_factor), "胸省长度")
        self.generator.add_increment("#sleeveLength", str(self.sleeve_length), "袖长")
        self.generator.add_increment("#sleeveWidth", str(self.sleeve_width * scale_factor), "袖宽")
        self.generator.add_increment("#sleeveCapHeight", str(self.sleeve_cap_height), "袖山高")
        self.generator.add_increment("#collarWidth", str(self.collar_width), "领宽")
        self.generator.add_increment("#buttonOverlap", str(self.button_overlap), "门襟重叠")
        
        # 创建各个部件
        self._create_professional_front_piece()
        self._create_professional_back_piece()
        self._create_professional_sleeve()
        self._create_professional_collar()
        
        # 保存版型
        pattern_file = self.generator.save_pattern(output_file)
        
        # 生成预览图
        if preview:
            preview_file = output_file.replace(".sm2d", "_preview.png")
            self.generate_preview(preview_file)
        
        return pattern_file
    
    def _create_professional_front_piece(self) -> None:
        """创建专业前片（精确按制版图设计）"""
        # 前片基本框架点
        p1 = self.generator.add_point("0", "0", "A1_前肩点", "base")
        p2 = self.generator.add_point("#shoulder_width", "0", "A2_前肩端")
        p3 = self.generator.add_point("0", "#bodyLength", "A3_前下摆左")
        p4 = self.generator.add_point("#bodyWidth/2 + #buttonOverlap", "#bodyLength", "A4_前下摆右")
        
        # 关键水平线点
        p5 = self.generator.add_point("0", "#chestLine", "A5_前胸围线左")
        p6 = self.generator.add_point("#bodyWidth/2", "#chestLine", "A6_前胸围线右")
        p7 = self.generator.add_point("0", "#waistLine", "A7_前腰围线左")
        p8 = self.generator.add_point("#bodyWidth/2 - 2.5", "#waistLine", "A8_前腰围线右")
        
        # 袖窿设计点
        p9 = self.generator.add_point("0", "#armholeDepth", "A9_前袖窿底左")
        p10 = self.generator.add_point("#bodyWidth/2", "#armholeDepth", "A10_前袖窿底右")
        p11 = self.generator.add_point("#bodyWidth/2 - 7.6", "#armholeDepth - 18", "A11_前袖窿顶")
        
        # 领口设计点
        p12 = self.generator.add_point("#shoulder_width/3", "0", "A12_前肩领点")
        p13 = self.generator.add_point("#buttonOverlap", "#neckDepthFront", "A13_前领底")
        p14 = self.generator.add_point("#buttonOverlap + 1", "#neckDepthFront - 1", "A14_前领控制点")
        
        # 胸省设计（按图纸精确定位）
        dart_center_x = "#bodyWidth/2 - 17.9"
        p15 = self.generator.add_point(dart_center_x, "#armholeDepth + 4", "A15_胸省顶")
        p16 = self.generator.add_point(f"{dart_center_x} - 2.5", "#armholeDepth + #dartLength", "A16_胸省左")
        p17 = self.generator.add_point(f"{dart_center_x} + 1.5", "#armholeDepth + #dartLength", "A17_胸省右")
        
        # 门襟线
        p18 = self.generator.add_point("#buttonOverlap", "0", "A18_门襟上")
        p19 = self.generator.add_point("#buttonOverlap", "#bodyLength", "A19_门襟下")
        
        # 下摆弧度控制点
        p20 = self.generator.add_point("0.7", "#bodyLength", "A20_下摆左控制")
        p21 = self.generator.add_point("#bodyWidth/2 + #buttonOverlap - 0.7", "#bodyLength", "A21_下摆右控制")
        
        # 创建线条（直线部分）
        self.generator.add_line(p1, p12, "前左肩线")
        self.generator.add_line(p12, p2, "前右肩线")
        self.generator.add_line(p13, p9, "前左侧线上")
        self.generator.add_line(p9, p7, "前左侧线中")
        self.generator.add_line(p7, p3, "前左侧线下")
        self.generator.add_line(p4, p8, "前右侧线下")
        self.generator.add_line(p8, p10, "前右侧线中")
        self.generator.add_line(p10, p11, "前袖窿底段")
        self.generator.add_line(p11, p2, "前袖窿顶段")
        self.generator.add_line(p18, p19, "前门襟线")
        self.generator.add_line(p15, p16, "前胸省左")
        self.generator.add_line(p15, p17, "前胸省右")
        
        # 暂时使用直线代替弧线（Seamly2D高级功能）
        # 未来版本将支持弧线设计
        
        # 创建前片
        front_contour = [p1, p12, p2, p11, p10, p8, p4, p19, p3, p9, p13]
        self.generator.create_piece("专业前片", front_contour, True)
    
    def _create_professional_back_piece(self) -> None:
        """创建专业后片"""
        # 后片偏移量
        offset_x = "#bodyWidth + 10"
        
        # 后片基本框架点
        p1 = self.generator.add_point(offset_x, "0", "B1_后肩点")
        p2 = self.generator.add_point(f"{offset_x} + #shoulder_width", "0", "B2_后肩端")
        p3 = self.generator.add_point(offset_x, "#bodyLength", "B3_后下摆左")
        p4 = self.generator.add_point(f"{offset_x} + #bodyWidth/2", "#bodyLength", "B4_后下摆右")
        
        # 关键水平线点
        p5 = self.generator.add_point(offset_x, "#chestLine", "B5_后胸围线左")
        p6 = self.generator.add_point(f"{offset_x} + #bodyWidth/2", "#chestLine", "B6_后胸围线右")
        p7 = self.generator.add_point(offset_x, "#waistLine", "B7_后腰围线左")
        p8 = self.generator.add_point(f"{offset_x} + #bodyWidth/2 - 1.5", "#waistLine", "B8_后腰围线右")
        
        # 袖窿设计点
        p9 = self.generator.add_point(offset_x, "#armholeDepth", "B9_后袖窿底左")
        p10 = self.generator.add_point(f"{offset_x} + #bodyWidth/2", "#armholeDepth", "B10_后袖窿底右")
        p11 = self.generator.add_point(f"{offset_x} + #bodyWidth/2 - 8.8", "#armholeDepth - 22", "B11_后袖窿顶")
        
        # 领口设计点
        p12 = self.generator.add_point(f"{offset_x} + #shoulder_width/3", "0", "B12_后肩领点")
        p13 = self.generator.add_point(offset_x, "#neckDepthBack", "B13_后领底")
        p14 = self.generator.add_point(f"{offset_x} + 1", "#neckDepthBack - 0.5", "B14_后领控制点")
        
        # 后片省道（如果需要）
        p15 = self.generator.add_point(f"{offset_x} + #bodyWidth/4", "#waistLine + 9", "B15_后省顶")
        p16 = self.generator.add_point(f"{offset_x} + #bodyWidth/4 - 1", "#waistLine + 18", "B16_后省左")
        p17 = self.generator.add_point(f"{offset_x} + #bodyWidth/4 + 1", "#waistLine + 18", "B17_后省右")
        
        # 创建线条
        self.generator.add_line(p1, p12, "后左肩线")
        self.generator.add_line(p12, p2, "后右肩线")
        self.generator.add_line(p13, p9, "后左侧线上")
        self.generator.add_line(p9, p7, "后左侧线中")
        self.generator.add_line(p7, p3, "后左侧线下")
        self.generator.add_line(p3, p4, "后下摆线")
        self.generator.add_line(p4, p8, "后右侧线下")
        self.generator.add_line(p8, p10, "后右侧线中")
        self.generator.add_line(p10, p11, "后袖窿底段")
        self.generator.add_line(p11, p2, "后袖窿顶段")
        self.generator.add_line(p15, p16, "后省左")
        self.generator.add_line(p15, p17, "后省右")
        
        # 暂时使用直线代替弧线
        # 未来版本将支持弧线设计
        
        # 创建后片
        back_contour = [p1, p12, p2, p11, p10, p8, p4, p3, p9, p13]
        self.generator.create_piece("专业后片", back_contour, False)
    
    def _create_professional_sleeve(self) -> None:
        """创建专业袖子（精确按图纸袖山弧度）"""
        # 袖子偏移位置
        offset_y = "#bodyLength + 30"
        
        # 袖子基本框架点
        p1 = self.generator.add_point("0", offset_y, "S1_袖底左")
        p2 = self.generator.add_point("#sleeveWidth", offset_y, "S2_袖底右")
        p3 = self.generator.add_point("8", f"{offset_y} + #sleeveLength", "S3_袖口左")
        p4 = self.generator.add_point("#sleeveWidth - 8", f"{offset_y} + #sleeveLength", "S4_袖口右")
        
        # 袖山关键点（按图纸精确定位）
        p5 = self.generator.add_point("#sleeveWidth/2", f"{offset_y} - #sleeveCapHeight", "S5_袖山顶")
        p6 = self.generator.add_point("#sleeveWidth/2 - 21.9", f"{offset_y} - 0.5", "S6_袖山左控制")
        p7 = self.generator.add_point("#sleeveWidth/2 + 21.9", f"{offset_y} - 0.5", "S7_袖山右控制")
        
        # 袖山弧度控制点
        p8 = self.generator.add_point("#sleeveWidth/2 - 10", f"{offset_y} - 15", "S8_袖山左弧控制")
        p9 = self.generator.add_point("#sleeveWidth/2 + 10", f"{offset_y} - 15", "S9_袖山右弧控制")
        
        # 袖口收缩点
        p10 = self.generator.add_point("12", f"{offset_y} + #sleeveLength - 15", "S10_袖口左收缩")
        p11 = self.generator.add_point("#sleeveWidth - 12", f"{offset_y} + #sleeveLength - 15", "S11_袖口右收缩")
        
        # 创建线条
        self.generator.add_line(p1, p10, "袖左侧上")
        self.generator.add_line(p10, p3, "袖左侧下")
        self.generator.add_line(p3, p4, "袖口线")
        self.generator.add_line(p4, p11, "袖右侧下")
        self.generator.add_line(p11, p2, "袖右侧上")
        
        # 袖山使用分段直线模拟弧度
        # 专业版本将在未来支持真正的弧线
        
        # 创建袖子
        sleeve_contour = [p1, p5, p2, p11, p4, p3, p10]
        self.generator.create_piece("专业袖子", sleeve_contour, False)
    
    def _create_professional_collar(self) -> None:
        """创建专业衬衫领（按图纸精确设计）"""
        # 领子偏移位置
        collar_offset_x = "#bodyWidth * 2 + 50"
        collar_offset_y = "15"
        
        # 按图纸计算领长（前领长+后领长=1.5的关系）
        collar_length = "#shoulder_width * 1.8 + 1.5"
        
        # 领子基本框架点
        p1 = self.generator.add_point(collar_offset_x, collar_offset_y, "C1_领底左")
        p2 = self.generator.add_point(f"{collar_offset_x} + {collar_length}", collar_offset_y, "C2_领底右")
        p3 = self.generator.add_point(collar_offset_x, f"{collar_offset_y} + #collarWidth", "C3_领上左")
        p4 = self.generator.add_point(f"{collar_offset_x} + {collar_length}", f"{collar_offset_y} + #collarWidth", "C4_领上右")
        
        # 领尖设计点（按图纸4度角设计）
        p5 = self.generator.add_point(f"{collar_offset_x} + 7", f"{collar_offset_y} + #collarWidth + 4", "C5_领尖左")
        p6 = self.generator.add_point(f"{collar_offset_x} + {collar_length} - 7", f"{collar_offset_y} + #collarWidth + 4", "C6_领尖右")
        
        # 领子弯曲控制点
        p7 = self.generator.add_point(f"{collar_offset_x} + {collar_length}/2", f"{collar_offset_y} + 2.5", "C7_领底弯曲控制")
        p8 = self.generator.add_point(f"{collar_offset_x} + {collar_length}/2", f"{collar_offset_y} + #collarWidth - 1", "C8_领上弯曲控制")
        
        # 创建线条
        self.generator.add_line(p2, p4, "领右侧")
        self.generator.add_line(p4, p6, "领上右段")
        self.generator.add_line(p6, p5, "领尖连线")
        self.generator.add_line(p5, p3, "领上左段")
        self.generator.add_line(p3, p1, "领左侧")
        
        # 领子弯曲使用直线近似
        # 专业弧线功能将在下一版本实现
        
        # 创建领子
        collar_contour = [p1, p2, p4, p6, p5, p3]
        self.generator.create_piece("专业领子", collar_contour, False)
    
    def generate_preview(self, output_file: str = "professional_womens_shirt_preview.png") -> str:
        """生成专业女式衬衫预览图
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            str: 输出文件的绝对路径
        """
        fig, ax = plt.subplots(figsize=(24, 16))
        
        # 提取关键尺寸
        bust = self.measurements.get("bust", 88.0)
        shoulder_width = self.measurements.get("shoulder_width", 38.0)
        length = self.measurements.get("length", 65.0)
        
        # 按比例计算
        scale_factor = bust / 88.0
        body_width = self.body_width * scale_factor
        body_length = self.body_length * scale_factor
        
        # 绘制各个部件
        self._draw_professional_front(ax, body_width, body_length, shoulder_width)
        self._draw_professional_back(ax, body_width, body_length, shoulder_width)
        self._draw_professional_sleeve(ax, body_length, scale_factor)
        self._draw_professional_collar(ax, body_width, shoulder_width)
        
        # 设置图表
        ax.set_aspect('equal')
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.set_title('专业女式衬衫版型预览 - 工业制版标准', fontsize=20, fontweight='bold')
        ax.set_xlabel('宽度 (cm)', fontsize=14)
        ax.set_ylabel('长度 (cm)', fontsize=14)
        
        # 设置坐标范围
        total_width = body_width * 3 + 100
        total_height = body_length + self.sleeve_length + 60
        ax.set_xlim(-10, total_width)
        ax.set_ylim(-30, total_height)
        
        # 反转Y轴以匹配Seamly2D坐标系
        ax.invert_yaxis()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=200, bbox_inches='tight')
        plt.close()
        
        logger.info(f"专业女式衬衫预览图已保存到: {output_file}")
        return os.path.abspath(output_file)
    
    def _draw_professional_front(self, ax, body_width, body_length, shoulder_width):
        """绘制专业前片"""
        # 前片主体轮廓
        front_points = [
            (0, 0),  # 肩点
            (shoulder_width/3, 0),  # 肩领点
            (self.button_overlap, self.neck_depth_front),  # 领底
            (0, self.armhole_depth),  # 袖窿底左
            (0, self.waist_line),  # 腰线左
            (0, body_length),  # 下摆左
            (self.button_overlap, body_length),  # 门襟下
            (body_width/2 + self.button_overlap, body_length),  # 下摆右
            (body_width/2 - 2.5, self.waist_line),  # 腰线右
            (body_width/2, self.armhole_depth),  # 袖窿底右
            (body_width/2 - 7.6, self.armhole_depth - 18),  # 袖窿顶
            (shoulder_width, 0),  # 肩端
        ]
        
        # 绘制轮廓
        for i in range(len(front_points)):
            p1 = front_points[i]
            p2 = front_points[(i + 1) % len(front_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'b-', linewidth=3)
        
        # 绘制胸省
        dart_x = body_width/2 - 17.9
        ax.plot([dart_x, dart_x - 2.5], [self.armhole_depth + 4, self.armhole_depth + 17.9], 'b--', linewidth=2)
        ax.plot([dart_x, dart_x + 1.5], [self.armhole_depth + 4, self.armhole_depth + 17.9], 'b--', linewidth=2)
        
        # 绘制门襟线
        ax.plot([self.button_overlap, self.button_overlap], [0, body_length], 'b:', linewidth=2)
        
        # 添加尺寸标注
        self._add_professional_front_dimensions(ax, body_width, body_length, shoulder_width)
        
        # 添加标题
        ax.text(body_width/4, -5, "专业前片", ha='center', fontsize=16, fontweight='bold', color='blue')
    
    def _draw_professional_back(self, ax, body_width, body_length, shoulder_width):
        """绘制专业后片"""
        offset_x = body_width + 10
        
        # 后片轮廓
        back_points = [
            (offset_x, 0),  # 肩点
            (offset_x + shoulder_width/3, 0),  # 肩领点
            (offset_x, self.neck_depth_back),  # 领底
            (offset_x, self.armhole_depth),  # 袖窿底左
            (offset_x, self.waist_line),  # 腰线左
            (offset_x, body_length),  # 下摆左
            (offset_x + body_width/2, body_length),  # 下摆右
            (offset_x + body_width/2 - 1.5, self.waist_line),  # 腰线右
            (offset_x + body_width/2, self.armhole_depth),  # 袖窿底右
            (offset_x + body_width/2 - 8.8, self.armhole_depth - 22),  # 袖窿顶
            (offset_x + shoulder_width, 0),  # 肩端
        ]
        
        # 绘制轮廓
        for i in range(len(back_points)):
            p1 = back_points[i]
            p2 = back_points[(i + 1) % len(back_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'g-', linewidth=3)
        
        # 绘制后省
        dart_x = offset_x + body_width/4
        ax.plot([dart_x, dart_x - 1], [self.waist_line + 9, self.waist_line + 18], 'g--', linewidth=2)
        ax.plot([dart_x, dart_x + 1], [self.waist_line + 9, self.waist_line + 18], 'g--', linewidth=2)
        
        # 添加尺寸标注
        self._add_professional_back_dimensions(ax, offset_x, body_width, body_length, shoulder_width)
        
        # 添加标题
        ax.text(offset_x + body_width/4, -5, "专业后片", ha='center', fontsize=16, fontweight='bold', color='green')
    
    def _draw_professional_sleeve(self, ax, body_length, scale_factor):
        """绘制专业袖子"""
        offset_y = body_length + 30
        sleeve_width = self.sleeve_width * scale_factor
        
        # 袖子轮廓（包含专业袖山弧度）
        sleeve_points = [
            (0, offset_y),  # 袖底左
            (sleeve_width/2 - 10, offset_y - 15),  # 袖山左弧控制
            (sleeve_width/2, offset_y - self.sleeve_cap_height),  # 袖山顶
            (sleeve_width/2 + 10, offset_y - 15),  # 袖山右弧控制
            (sleeve_width, offset_y),  # 袖底右
            (sleeve_width - 12, offset_y + self.sleeve_length - 15),  # 袖口右收缩
            (sleeve_width - 8, offset_y + self.sleeve_length),  # 袖口右
            (8, offset_y + self.sleeve_length),  # 袖口左
            (12, offset_y + self.sleeve_length - 15),  # 袖口左收缩
        ]
        
        # 绘制轮廓
        for i in range(len(sleeve_points)):
            p1 = sleeve_points[i]
            p2 = sleeve_points[(i + 1) % len(sleeve_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'r-', linewidth=3)
        
        # 添加尺寸标注
        self._add_professional_sleeve_dimensions(ax, offset_y, sleeve_width)
        
        # 添加标题
        ax.text(sleeve_width/2, offset_y + self.sleeve_length + 8, "专业袖子", 
               ha='center', fontsize=16, fontweight='bold', color='red')
    
    def _draw_professional_collar(self, ax, body_width, shoulder_width):
        """绘制专业领子"""
        collar_offset_x = body_width * 2 + 50
        collar_offset_y = 15
        collar_length = shoulder_width * 1.8 + 1.5
        
        # 领子轮廓（包含弯曲度）
        collar_points = [
            (collar_offset_x, collar_offset_y),  # 领底左
            (collar_offset_x + collar_length/2, collar_offset_y + 2.5),  # 领底弯曲
            (collar_offset_x + collar_length, collar_offset_y),  # 领底右
            (collar_offset_x + collar_length, collar_offset_y + self.collar_width),  # 领上右
            (collar_offset_x + collar_length - 7, collar_offset_y + self.collar_width + 4),  # 领尖右
            (collar_offset_x + 7, collar_offset_y + self.collar_width + 4),  # 领尖左
            (collar_offset_x, collar_offset_y + self.collar_width),  # 领上左
        ]
        
        # 绘制轮廓
        for i in range(len(collar_points)):
            p1 = collar_points[i]
            p2 = collar_points[(i + 1) % len(collar_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'm-', linewidth=3)
        
        # 添加尺寸标注
        self._add_professional_collar_dimensions(ax, collar_offset_x, collar_offset_y, collar_length)
        
        # 添加标题
        ax.text(collar_offset_x + collar_length/2, collar_offset_y + self.collar_width + 12, "专业领子", 
               ha='center', fontsize=16, fontweight='bold', color='magenta')
    
    def _add_professional_front_dimensions(self, ax, body_width, body_length, shoulder_width):
        """添加专业前片尺寸标注"""
        # 身宽标注
        ax.annotate('', xy=(0, self.chest_line), xytext=(body_width/2, self.chest_line),
                   arrowprops=dict(arrowstyle='<->', color='blue', lw=2))
        ax.text(body_width/4, self.chest_line - 2, f'身宽/2: {body_width/2:.1f}cm', 
               ha='center', va='top', fontsize=11, fontweight='bold', color='blue')
        
        # 身长标注
        ax.annotate('', xy=(-5, 0), xytext=(-5, body_length),
                   arrowprops=dict(arrowstyle='<->', color='blue', lw=2))
        ax.text(-7, body_length/2, f'身长: {body_length:.1f}cm', ha='right', va='center', 
               rotation=90, fontsize=11, fontweight='bold', color='blue')
        
        # 袖窿深标注
        ax.text(body_width/2 + 3, self.armhole_depth/2, f'袖窿深: {self.armhole_depth:.1f}cm', 
               ha='left', va='center', rotation=90, fontsize=10, fontweight='bold', color='blue')
        
        # 胸省长度标注
        ax.text(body_width/2 - 15, self.armhole_depth + 10, f'胸省: {self.dart_length:.1f}cm', 
               ha='center', va='center', fontsize=10, fontweight='bold', color='blue')
    
    def _add_professional_back_dimensions(self, ax, offset_x, body_width, body_length, shoulder_width):
        """添加专业后片尺寸标注"""
        ax.text(offset_x + body_width/4, self.chest_line - 2, f'后身宽/2: {body_width/2:.1f}cm', 
               ha='center', va='top', fontsize=11, fontweight='bold', color='green')
    
    def _add_professional_sleeve_dimensions(self, ax, offset_y, sleeve_width):
        """添加专业袖子尺寸标注"""
        # 袖长标注
        ax.annotate('', xy=(-5, offset_y), xytext=(-5, offset_y + self.sleeve_length),
                   arrowprops=dict(arrowstyle='<->', color='red', lw=2))
        ax.text(-7, offset_y + self.sleeve_length/2, f'袖长: {self.sleeve_length:.1f}cm', 
               ha='right', va='center', rotation=90, fontsize=11, fontweight='bold', color='red')
        
        # 袖宽标注
        ax.text(sleeve_width/2, offset_y + 3, f'袖宽: {sleeve_width:.1f}cm', 
               ha='center', va='bottom', fontsize=11, fontweight='bold', color='red')
        
        # 袖山高标注
        ax.text(sleeve_width/2 + 15, offset_y - self.sleeve_cap_height/2, f'袖山高: {self.sleeve_cap_height:.1f}cm', 
               ha='left', va='center', fontsize=10, fontweight='bold', color='red')
    
    def _add_professional_collar_dimensions(self, ax, offset_x, offset_y, collar_length):
        """添加专业领子尺寸标注"""
        # 领长标注
        ax.text(offset_x + collar_length/2, offset_y - 3, f'领长: {collar_length:.1f}cm', 
               ha='center', va='top', fontsize=11, fontweight='bold', color='magenta')
        
        # 领宽标注
        ax.text(offset_x - 3, offset_y + self.collar_width/2, f'领宽: {self.collar_width:.1f}cm', 
               ha='right', va='center', rotation=90, fontsize=11, fontweight='bold', color='magenta') 