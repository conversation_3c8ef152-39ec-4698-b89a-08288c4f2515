<?xml version="1.0" encoding="UTF-8"?>
   <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
         <!-- XML Schema Generated from XML Document-->
         <xs:element name="pattern">
               <xs:complexType>
                     <xs:sequence minOccurs="1" maxOccurs="unbounded">
							<xs:element name="version" type="formatVersion"></xs:element>
							<xs:element name="author" type="xs:string" minOccurs="0" maxOccurs="1"></xs:element>
							<xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1"></xs:element>
							<xs:element name="notes" type="xs:string" minOccurs="0" maxOccurs="1"></xs:element>
							<xs:element name="gradation" minOccurs="0" maxOccurs="1">
								<xs:complexType>
                                       <xs:sequence>
                                             <xs:element name="heights">
                                                   <xs:complexType>
                                                         <xs:attribute name="all" type="xs:boolean" use="required"></xs:attribute>
                                                         <xs:attribute name="h92" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h98" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h104" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h110" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h116" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h122" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h128" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h134" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h140" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h146" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h152" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h158" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h164" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h170" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h176" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h182" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h188" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="h194" type="xs:boolean"></xs:attribute>	   
                                                   </xs:complexType>
                                             </xs:element>
                                             <xs:element name="sizes">
                                                   <xs:complexType>
                                                         <xs:attribute name="all" type="xs:boolean" use="required"></xs:attribute>
                                                         <xs:attribute name="s22" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s24" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s26" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s28" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s30" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s32" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s34" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s36" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s38" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s40" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s42" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s44" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s46" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s48" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s50" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s52" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s54" type="xs:boolean"></xs:attribute>
														 <xs:attribute name="s56" type="xs:boolean"></xs:attribute>	   
                                                   </xs:complexType>
                                             </xs:element>
                                       </xs:sequence>
                                 </xs:complexType>
							</xs:element>
							<xs:element name="measurements" minOccurs="0" maxOccurs="unbounded">
								<xs:complexType>
									<xs:attribute name="type" type="measurementsTypes" use="required"></xs:attribute>
									<xs:attribute name="path" type="xs:string" use="required"></xs:attribute>
									<xs:attribute name="unit" type="units" use="required"></xs:attribute>   
							   	</xs:complexType>
							</xs:element>
                           <xs:element name="increments" minOccurs="0" maxOccurs="1">
                                 <xs:complexType>
                                       <xs:sequence minOccurs="0" maxOccurs="unbounded">
                                             <xs:element name="increment" minOccurs="0" maxOccurs="unbounded">
                                                   <xs:complexType>
                                                         <xs:attribute name="ksize" type="xs:double" use="required"></xs:attribute>
													     <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
														 <xs:attribute name="description" type="xs:string" use="required"></xs:attribute>
														 <xs:attribute name="kgrowth" type="xs:double" use="required"></xs:attribute>
														 <xs:attribute name="name" type="shortName" use="required"></xs:attribute>
														 <xs:attribute name="base" type="xs:double" use="required"></xs:attribute>	   
                                                   </xs:complexType>
                                             </xs:element>
                                       </xs:sequence>
                                 </xs:complexType>
                           </xs:element>
                           <xs:element name="draw" minOccurs="1" maxOccurs="unbounded">
                                 <xs:complexType>
                                       <xs:sequence>
                                             <xs:element name="calculation" minOccurs="1" maxOccurs="unbounded">
                                                   <xs:complexType>
                                                         <xs:sequence>
														       <xs:choice minOccurs="0" maxOccurs="unbounded">	 
                                                                     <xs:element name="point" minOccurs="0" maxOccurs="unbounded">
																	       <xs:complexType>
																		         <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
																			     <xs:attribute name="x" type="xs:double"></xs:attribute>
																			     <xs:attribute name="y" type="xs:double"></xs:attribute>
																			     <xs:attribute name="mx" type="xs:double"></xs:attribute>
																			     <xs:attribute name="my" type="xs:double"></xs:attribute>
																			     <xs:attribute name="type" type="xs:string"></xs:attribute>
																			     <xs:attribute name="name" type="shortName"></xs:attribute>
																			     <xs:attribute name="firstPoint" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="secondPoint" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="thirdPoint" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="basePoint" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="pShoulder" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="p1Line" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="p2Line" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="length" type="xs:string"></xs:attribute>
																			     <xs:attribute name="angle" type="xs:string"></xs:attribute>
																			     <xs:attribute name="typeLine" type="xs:string"></xs:attribute>
																			     <xs:attribute name="splinePath" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="spline" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="p1Line1" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="p1Line2" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="p2Line1" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="p2Line2" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="center" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="radius" type="xs:string"></xs:attribute>
																			     <xs:attribute name="axisP1" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="axisP2" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="arc" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="curve" type="xs:unsignedInt"></xs:attribute>
																	       </xs:complexType>
                                                                     </xs:element>                                                              
																     <xs:element name="line" minOccurs="0" maxOccurs="unbounded">
																		   <xs:complexType>
																			     <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
																			     <xs:attribute name="firstPoint" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="secondPoint" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="typeLine" type="xs:string"></xs:attribute>
																		   </xs:complexType>
																     </xs:element> 
																     <xs:element name="arc" minOccurs="0" maxOccurs="unbounded">
																		   <xs:complexType>
																			     <xs:attribute name="angle1" type="xs:string"></xs:attribute>
																			     <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
																			     <xs:attribute name="angle2" type="xs:string"></xs:attribute>
																			     <xs:attribute name="radius" type="xs:string"></xs:attribute>
																			     <xs:attribute name="center" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="type" type="xs:string"></xs:attribute>
																		   </xs:complexType>
																     </xs:element>
																     <xs:element name="spline" minOccurs="0" maxOccurs="unbounded">
																		   <xs:complexType>
																			     <xs:sequence>
																					   <xs:element name="pathPoint" minOccurs="0" maxOccurs="unbounded">
																						     <xs:complexType>
																								   <xs:attribute name="kAsm2" type="xs:string"></xs:attribute>
																								   <xs:attribute name="pSpline" type="xs:unsignedInt"></xs:attribute>
																								   <xs:attribute name="angle" type="xs:string"></xs:attribute>
																								   <xs:attribute name="kAsm1" type="xs:string"></xs:attribute>
																						     </xs:complexType>
																					   </xs:element>
																			     </xs:sequence>
																			     <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
																			     <xs:attribute name="kCurve" type="xs:double"></xs:attribute>
																			     <xs:attribute name="type" type="xs:string"></xs:attribute>
																			     <xs:attribute name="kAsm1" type="xs:double"></xs:attribute>
																			     <xs:attribute name="kAsm2" type="xs:double"></xs:attribute>
																			     <xs:attribute name="angle1" type="xs:double"></xs:attribute>
																			     <xs:attribute name="angle2" type="xs:double"></xs:attribute>
																			     <xs:attribute name="point1" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="point4" type="xs:unsignedInt"></xs:attribute>
																		   </xs:complexType>
																     </xs:element>
                                                               </xs:choice>
                                                         </xs:sequence>
                                                   </xs:complexType>
                                             </xs:element>
                                             <xs:element name="modeling" minOccurs="1" maxOccurs="unbounded">
                                                   <xs:complexType>
                                                         <xs:sequence>
														       <xs:choice minOccurs="0" maxOccurs="unbounded">
																     <xs:element name="point" minOccurs="0" maxOccurs="unbounded">
																		   <xs:complexType>
																			     <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
																			     <xs:attribute name="idObject" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="mx" type="xs:double"></xs:attribute>
																			     <xs:attribute name="typeObject" type="xs:string"></xs:attribute>
																			     <xs:attribute name="my" type="xs:double"></xs:attribute>
																			     <xs:attribute name="type" type="xs:string"></xs:attribute>
																			     <xs:attribute name="idTool" type="xs:unsignedInt"></xs:attribute>
																		   </xs:complexType>
																     </xs:element>
																     <xs:element name="arc" minOccurs="0" maxOccurs="unbounded">
																		   <xs:complexType>
																			     <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
																			     <xs:attribute name="idObject" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="typeObject" type="xs:string"></xs:attribute>
																			     <xs:attribute name="type" type="xs:string"></xs:attribute>
																			     <xs:attribute name="idTool" type="xs:unsignedInt"></xs:attribute>
																		   </xs:complexType>
																     </xs:element>
																     <xs:element name="spline" minOccurs="0" maxOccurs="unbounded">
																		   <xs:complexType>
																			     <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
																			     <xs:attribute name="idObject" type="xs:unsignedInt"></xs:attribute>
																			     <xs:attribute name="typeObject" type="xs:string"></xs:attribute>
																			     <xs:attribute name="type" type="xs:string"></xs:attribute>
																			     <xs:attribute name="idTool" type="xs:unsignedInt"></xs:attribute>
																		   </xs:complexType>
																     </xs:element>
																     <xs:element name="tools" minOccurs="0" maxOccurs="unbounded">
                                                                       <xs:complexType>
                                                                             <xs:sequence>
                                                                                   <xs:element name="det" minOccurs="2" maxOccurs="2">
                                                                                         <xs:complexType>
                                                                                               <xs:sequence>
                                                                                                     <xs:element name="node" maxOccurs="unbounded">
                                                                                                           <xs:complexType>
                                                                                                                 <xs:attribute name="nodeType" type="xs:string"></xs:attribute>
                                                                                                                 <xs:attribute name="idObject" type="xs:unsignedInt"></xs:attribute>
                                                                                                                 <xs:attribute name="mx" type="xs:double"></xs:attribute>
                                                                                                                 <xs:attribute name="my" type="xs:double"></xs:attribute>
                                                                                                                 <xs:attribute name="type" type="xs:string"></xs:attribute>
                                                                                                           </xs:complexType>
                                                                                                     </xs:element>
                                                                                               </xs:sequence>
                                                                                         </xs:complexType>
                                                                                   </xs:element>
                                                                             </xs:sequence>
                                                                             <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
                                                                             <xs:attribute name="type" type="xs:string"></xs:attribute>
                                                                             <xs:attribute name="indexD1" type="xs:unsignedInt"></xs:attribute>
                                                                             <xs:attribute name="indexD2" type="xs:unsignedInt"></xs:attribute>
                                                                       </xs:complexType>
                                                                    </xs:element>
                                                               </xs:choice>           
                                                         </xs:sequence>
                                                   </xs:complexType>
                                             </xs:element>
                                             <xs:element name="details" minOccurs="1" maxOccurs="unbounded">
                                                   <xs:complexType>
                                                         <xs:sequence>
                                                               <xs:element name="detail" minOccurs="0" maxOccurs="unbounded">
                                                                     <xs:complexType>
                                                                           <xs:sequence>
                                                                                 <xs:element name="node" maxOccurs="unbounded">
                                                                                       <xs:complexType>
                                                                                             <xs:attribute name="nodeType" type="xs:string"></xs:attribute>
                                                                                             <xs:attribute name="idObject" type="xs:unsignedInt"></xs:attribute>
                                                                                             <xs:attribute name="mx" type="xs:double"></xs:attribute>
                                                                                             <xs:attribute name="my" type="xs:double"></xs:attribute>
                                                                                             <xs:attribute name="type" type="xs:string"></xs:attribute>
                                                                                       </xs:complexType>
                                                                                 </xs:element>
                                                                           </xs:sequence>
                                                                           <xs:attribute name="id" type="xs:unsignedInt" use="required"></xs:attribute>
                                                                           <xs:attribute name="supplement" type="xs:unsignedInt"></xs:attribute>
                                                                           <xs:attribute name="mx" type="xs:double"></xs:attribute>
                                                                           <xs:attribute name="my" type="xs:double"></xs:attribute>
                                                                           <xs:attribute name="width" type="xs:double"></xs:attribute>
                                                                           <xs:attribute name="name" type="xs:string"></xs:attribute>
                                                                           <xs:attribute name="closed" type="xs:unsignedInt"></xs:attribute>
                                                                     </xs:complexType>
                                                               </xs:element>
                                                         </xs:sequence>
                                                   </xs:complexType>
                                             </xs:element>
                                       </xs:sequence>
                                       <xs:attribute name="name" type="xs:string"></xs:attribute>
                                 </xs:complexType>
                           </xs:element>
                     </xs:sequence>
               </xs:complexType>
         </xs:element>
	<xs:simpleType name="shortName">
		<xs:restriction base="xs:string">   
			<xs:pattern value="^([^0-9-*/^+=\s\(\)%:;!.,`'\&quot;]){1,1}([^-*/^+=\s\(\)%:;!.,`'\&quot;]){0,}$"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="units">
		<xs:restriction base="xs:string">
			<xs:enumeration value="mm"/>
			<xs:enumeration value="cm"/>
			<xs:enumeration value="inch"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="measurementsTypes">
		<xs:restriction base="xs:string">
			<xs:enumeration value="standard"/>
			<xs:enumeration value="individual"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="formatVersion">
		<xs:restriction base="xs:string">
			<xs:pattern value="^(0|([1-9][0-9]*))\.(0|([1-9][0-9]*))\.(0|([1-9][0-9]*))$"/>
		</xs:restriction>
	</xs:simpleType>
   </xs:schema>
