<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogAboutSeamlyMe</class>
 <widget class="QDialog" name="DialogAboutSeamlyMe">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>462</width>
    <height>343</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>About SeamlyMe</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/logos/seamlyme_logo_32.png</normaloff>:/icon/logos/seamlyme_logo_32.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="spacing">
      <number>4</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>9</number>
     </property>
     <item>
      <widget class="QLabel" name="label_appIcon">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap resource="../../../libs/vmisc/share/resources/icon.qrc">:/icon/logos/seamlyme_logo_96.png</pixmap>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="label_SeamlyMe_Version">
         <property name="font">
          <font>
           <pointsize>15</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="text">
          <string>SeamlyMe version</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignHCenter">
        <widget class="QLabel" name="labelBuildRevision">
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="accessibleName">
          <string/>
         </property>
         <property name="accessibleDescription">
          <string/>
         </property>
         <property name="text">
          <string notr="true">Build revision:</string>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignHCenter">
        <widget class="QLabel" name="label">
         <property name="text">
          <string>This program is part of Seamly2D project.</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_Web_Site">
         <property name="palette">
          <palette>
           <active>
            <colorrole role="ButtonText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
           </active>
           <inactive>
            <colorrole role="ButtonText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
           </inactive>
           <disabled>
            <colorrole role="ButtonText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>120</red>
               <green>120</green>
               <blue>120</blue>
              </color>
             </brush>
            </colorrole>
           </disabled>
          </palette>
         </property>
         <property name="font">
          <font>
           <underline>true</underline>
          </font>
         </property>
         <property name="cursor">
          <cursorShape>PointingHandCursor</cursorShape>
         </property>
         <property name="text">
          <string notr="true">pushButton_Web_Site</string>
         </property>
         <property name="autoDefault">
          <bool>false</bool>
         </property>
         <property name="flat">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_SeamlyMe_Built">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string notr="true">label_SeamlyMe_Built</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label_QT_Version">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string notr="true">label_QT_Version</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="label_Legal_Stuff">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="text">
          <string notr="true">label_Legal_Stuff</string>
         </property>
         <property name="wordWrap">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QProgressBar" name="downloadProgress">
         <property name="value">
          <number>25</number>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="textVisible">
          <bool>true</bool>
         </property>
         <property name="invertedAppearance">
          <bool>false</bool>
         </property>
         <property name="format">
          <string>Downloading installer %p% complete</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QPushButton" name="pushButtonCheckUpdate">
       <property name="text">
        <string>Check For Updates</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDialogButtonBox" name="buttonBox">
       <property name="autoFillBackground">
        <bool>true</bool>
       </property>
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="standardButtons">
        <set>QDialogButtonBox::Ok</set>
       </property>
       <property name="centerButtons">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections/>
</ui>
