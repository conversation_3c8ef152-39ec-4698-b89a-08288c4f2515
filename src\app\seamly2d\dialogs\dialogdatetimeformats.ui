<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogDateTimeFormats</class>
 <widget class="QDialog" name="DialogDateTimeFormats">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>393</width>
    <height>381</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Label date time editor</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/64x64/icon64x64.png</normaloff>:/icon/64x64/icon64x64.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QListWidget" name="listWidget"/>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="text">
        <string>Format:</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QLineEdit" name="lineEditFormat">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="statusTip">
        <string/>
       </property>
       <property name="placeholderText">
        <string>Insert a format</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="toolButtonAdd">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="text">
        <string notr="true">Add</string>
       </property>
       <property name="icon">
        <iconset theme="list-add">
         <normaloff>.</normaloff>.</iconset>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="toolButtonRemove">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="text">
        <string notr="true">Remove</string>
       </property>
       <property name="icon">
        <iconset theme="list-remove">
         <normaloff>.</normaloff>.</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>DialogDateTimeFormats</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>DialogDateTimeFormats</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
