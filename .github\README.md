![Seamly2D banner](../share/img/Seamly2D_banner_1202x271.png)

-----




[![GitHub release (latest)](https://img.shields.io/github/v/release/fashionfreedom/seamly2d?logo=github?color=blue&style=flat-square)](https://github.com/FashionFreedom/Seamly2D/releases/latest)[![GitHub weekly build](https://img.shields.io/github/actions/workflow/status/fashionfreedom/seamly2d/ci.yml?logo=github&style=flat-square)](https://github.com/FashionFreedom/Seamly2D/actions/workflows/ci.yml)[![GitHub commit activity](https://img.shields.io/github/commit-activity/m/fashionfreedom/seamly2d?logo=github&color=brightgreen&style=flat-square)](https://github.com/FashionFreedom/Seamly2D/graphs/commit-activity)
[![GitHub contributors](https://img.shields.io/github/contributors/fashionfreedom/seamly2d?style=flat-square&logo=github)](https://github.com/FashionFreedom/Seamly2D/graphs/contributors)[![GitHub](https://img.shields.io/github/license/fashionfreedom/seamly2d?color=blue&style=flat-square&logo=creativecommons)](../LICENSE)![GitHub language count](https://img.shields.io/github/languages/count/fashionfreedom/seamly2d?style=flat-square&logo=github)
[![Website](https://img.shields.io/website?down_message=Down&style=flat-square&up_color=brightgreen&up_message=Up&url=https%3A%2F%2Fseamly.io&logo=wordpress)](https://seamly.io/)[![Forum posts](https://img.shields.io/discourse/posts?server=https%3A%2F%2Fforum.seamly.io&style=flat-square&logo=discourse)](https://forum.seamly.io/)  
[![Qt](https://sourceforge.net/p/seamly2d/wiki/_discuss/thread/65e38fbf32/a54b/attachment/Built_with_Qt_RGB_logo_vertical_transparent_60x65px.png)](https://qt.io)  

Click here -->[![GitHub Stars](https://img.shields.io/github/stars/fashionfreedom/seamly2d?style=social)](https://github.com/fashionfreedom/seamly2d)<-- to star Seamly2D.



<big>Seamly2D</big> is open source patternmaking software to democratize fashion, released under the GPLv3+ license, and available for Windows, MacOS, and Linux.

Design what **you** want to wear. Each Seamly2D pattern can read multi-size measurement files for standardized sizes *and* read individual measurement files for custom-fit.
Don't know how? Join our multi-lingual Discourse [forum](https://forum.seamly.io).

### Supported platforms:
   * Windows 10 & 11 (32-bit and 64-bit) 
   * macOS 64-bit Ventura (13) and Sonoma (14)
   * Most current Linux distros as Flatpak via [Flathub](https://flathub.org/apps/io.seamly.seamly2d)
   * Most current Linux distros as AppImage
___________________________________________________
### Download:

| Windows 64-bit | Windows 32-bit | MacOS | Linux AppImage | Linux Flatpak |
| :---:          | :---:          | :---: | :---:          | :---:         |
| [![Seamly2D-windows.zip](./img/Microsoft_logo-60x60px.png)](https://github.com/FashionFreedom/Seamly2D/releases/latest/download/Seamly2D-windows.zip) | [![Seamly2D-win32.zip](./img/Microsoft_logo-60x60px.png)](https://github.com/FashionFreedom/Seamly2D/releases/latest/download/Seamly2D-win32.zip) | [![Seamly2D-macos.zip](./img/MacOS_logo_60x60.png)](https://github.com/FashionFreedom/Seamly2D/releases/latest/download/Seamly2D-macos.zip) | [![Seamly2D AppImage](./img/linux-svgrepo-com-60x71.png)](https://github.com/FashionFreedom/Seamly2D/releases/latest/download/Seamly2D-x86_64.AppImage) | [![Seamly2D Flatpak](./img/flathub-badge-en.png)](https://flathub.org/apps/io.seamly.seamly2d) |
| Intel or AMD 64bit | Intel or AMD 32bit | | GCC 10 or later | GCC 10 or later |
| Windows 10<br>Windows 11 | Windows 10 | macOS 13 Ventura<br>macOS 14 Sonoma | Debian 10 Buster<br>Debian 11 Bullseye<br>Debian 12 Bookworm<br>Ubuntu 22.04 Jammy<br>Ubuntu 23.04 Lunar<br>Up-to-date Arch Linux -<br>Manjaro<br>ArcoLinux<br>EndeavourOS<br>Anarchy<br>ArchLabs|Debian 10 Buster<br>Debian 11 Bullseye<br>Debian 12 Bookworm<br>Ubuntu 22.04 Jammy<br>Ubuntu 23.04 Lunar<br>Up-to-date Arch Linux -<br>Manjaro<br>ArcoLinux<br>EndeavourOS<br>Anarchy,<br>ArchLabs|

___________________________________________________
### Community :
   * [Website and downloads](https://seamly.io) - _active, current_
   * [User Forum](https://forum.seamly.io/) - _active, current_
   * [User Wiki Manual](https://wiki.seamly.io) - _needs updating_
___________________________________________________

### Developer :
   * [Developer README](README-DEVELOPER.md) - _current_
   * [Developer Wiki](https://github.com/FashionFreedom/Seamly2D/wiki) - _needs updating_
   * [Doxygen docs](https://fashionfreedom.github.io/Seamly2D/) - _current_
___________________________________________________
### LICENSING

![GPLv3](./img/GPLv3_Logo-121x60px.png)

See [https://www.gnu.org/licenses/gpl-3.0.html](https://www.gnu.org/licenses/gpl-3.0.html) for more information.

Other components and licensing:
* QMuParser - [Simplified BSD license](https://opensource.org/licenses/BSD-2-Clause)
* VPropertyExplorer - [LGPLv2.1 license](https://www.gnu.org/licenses/old-licenses/lgpl-2.1.en.html)
