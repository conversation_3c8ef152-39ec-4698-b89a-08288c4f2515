<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>HistoryDialog</class>
 <widget class="QDialog" name="HistoryDialog">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>801</width>
    <height>550</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="palette">
   <palette>
    <active>
     <colorrole role="Highlight">
      <brush brushstyle="SolidPattern">
       <color alpha="255">
        <red>217</red>
        <green>217</green>
        <blue>217</blue>
       </color>
      </brush>
     </colorrole>
     <colorrole role="HighlightedText">
      <brush brushstyle="SolidPattern">
       <color alpha="255">
        <red>0</red>
        <green>0</green>
        <blue>0</blue>
       </color>
      </brush>
     </colorrole>
    </active>
    <inactive>
     <colorrole role="Highlight">
      <brush brushstyle="SolidPattern">
       <color alpha="255">
        <red>217</red>
        <green>217</green>
        <blue>217</blue>
       </color>
      </brush>
     </colorrole>
     <colorrole role="HighlightedText">
      <brush brushstyle="SolidPattern">
       <color alpha="255">
        <red>0</red>
        <green>0</green>
        <blue>0</blue>
       </color>
      </brush>
     </colorrole>
    </inactive>
    <disabled>
     <colorrole role="Highlight">
      <brush brushstyle="SolidPattern">
       <color alpha="255">
        <red>0</red>
        <green>120</green>
        <blue>215</blue>
       </color>
      </brush>
     </colorrole>
     <colorrole role="HighlightedText">
      <brush brushstyle="SolidPattern">
       <color alpha="255">
        <red>0</red>
        <green>0</green>
        <blue>0</blue>
       </color>
      </brush>
     </colorrole>
    </disabled>
   </palette>
  </property>
  <property name="font">
   <font>
    <pointsize>10</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>History</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/64x64/icon64x64.png</normaloff>:/icon/64x64/icon64x64.png</iconset>
  </property>
  <property name="locale">
   <locale language="English" country="UnitedStates"/>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="rightMargin">
    <number>9</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QToolButton" name="clipboard_ToolButton">
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/32x32/clipboard_icon.png</normaloff>:/icon/32x32/clipboard_icon.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>20</width>
           <height>20</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="findIcon_Label">
         <property name="maximumSize">
          <size>
           <width>16</width>
           <height>16</height>
          </size>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="pixmap">
          <pixmap resource="../../../libs/vmisc/share/resources/icon.qrc">:/icon/32x32/find.png</pixmap>
         </property>
         <property name="scaledContents">
          <bool>true</bool>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="findText_Label">
         <property name="font">
          <font>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="text">
          <string>Find:</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="find_LineEdit">
         <property name="font">
          <font>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="mouseTracking">
          <bool>true</bool>
         </property>
         <property name="placeholderText">
          <string>Search text</string>
         </property>
         <property name="clearButtonEnabled">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="prev_PushButton">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="toolTip">
          <string>Find previous</string>
         </property>
         <property name="text">
          <string notr="true"/>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/theme.qrc">
           <normaloff>:/icons/win.icon.theme/32x32/actions/go-previous.png</normaloff>:/icons/win.icon.theme/32x32/actions/go-previous.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>24</width>
           <height>18</height>
          </size>
         </property>
         <property name="flat">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="next_PushButton">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="toolTip">
          <string>Find next</string>
         </property>
         <property name="text">
          <string notr="true"/>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/theme.qrc">
           <normaloff>:/icons/win.icon.theme/32x32/actions/go-next.png</normaloff>:/icons/win.icon.theme/32x32/actions/go-next.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>24</width>
           <height>18</height>
          </size>
         </property>
         <property name="flat">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QToolButton" name="regex_ToolButton">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="toolTip">
          <string>Seach by regular expression</string>
         </property>
         <property name="text">
          <string notr="true"/>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/svg/regex.svg</normaloff>:/icon/svg/regex.svg</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>24</width>
           <height>18</height>
          </size>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QToolButton" name="case_ToolButton">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="toolTip">
          <string>Case sensitive</string>
         </property>
         <property name="text">
          <string notr="true"/>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/svg/case.svg</normaloff>:/icon/svg/case.svg</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>24</width>
           <height>18</height>
          </size>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QToolButton" name="word_ToolButton">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>30</width>
           <height>24</height>
          </size>
         </property>
         <property name="toolTip">
          <string>Search by full word</string>
         </property>
         <property name="text">
          <string notr="true"/>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/svg/word.svg</normaloff>:/icon/svg/word.svg</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>24</width>
           <height>18</height>
          </size>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QTableWidget" name="tableWidget">
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Highlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>162</red>
             <green>188</green>
             <blue>216</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Highlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>162</red>
             <green>188</green>
             <blue>216</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Highlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>0</red>
             <green>120</green>
             <blue>215</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="font">
        <font>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="mouseTracking">
        <bool>false</bool>
       </property>
       <property name="focusPolicy">
        <enum>Qt::NoFocus</enum>
       </property>
       <property name="styleSheet">
        <string notr="true">QHeaderView::section {
	background-color: rgb(227, 227, 227);
    border: 1px solid black;
}</string>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Sunken</enum>
       </property>
       <property name="lineWidth">
        <number>1</number>
       </property>
       <property name="alternatingRowColors">
        <bool>true</bool>
       </property>
       <property name="selectionBehavior">
        <enum>QAbstractItemView::SelectRows</enum>
       </property>
       <property name="iconSize">
        <size>
         <width>24</width>
         <height>24</height>
        </size>
       </property>
       <property name="wordWrap">
        <bool>false</bool>
       </property>
       <property name="cornerButtonEnabled">
        <bool>true</bool>
       </property>
       <property name="columnCount">
        <number>3</number>
       </property>
       <attribute name="horizontalHeaderVisible">
        <bool>true</bool>
       </attribute>
       <attribute name="horizontalHeaderMinimumSectionSize">
        <number>30</number>
       </attribute>
       <attribute name="horizontalHeaderDefaultSectionSize">
        <number>40</number>
       </attribute>
       <attribute name="horizontalHeaderHighlightSections">
        <bool>false</bool>
       </attribute>
       <attribute name="horizontalHeaderStretchLastSection">
        <bool>true</bool>
       </attribute>
       <attribute name="verticalHeaderVisible">
        <bool>true</bool>
       </attribute>
       <attribute name="verticalHeaderMinimumSectionSize">
        <number>24</number>
       </attribute>
       <attribute name="verticalHeaderDefaultSectionSize">
        <number>24</number>
       </attribute>
       <attribute name="verticalHeaderHighlightSections">
        <bool>false</bool>
       </attribute>
       <column>
        <property name="text">
         <string>Id</string>
        </property>
        <property name="font">
         <font>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="background">
         <color>
          <red>255</red>
          <green>255</green>
          <blue>255</blue>
         </color>
        </property>
       </column>
       <column>
        <property name="text">
         <string>Name</string>
        </property>
        <property name="font">
         <font>
          <pointsize>10</pointsize>
         </font>
        </property>
       </column>
       <column>
        <property name="text">
         <string>Description</string>
        </property>
        <property name="font">
         <font>
          <pointsize>10</pointsize>
         </font>
        </property>
       </column>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <tabstops>
  <tabstop>tableWidget</tabstop>
  <tabstop>buttonBox</tabstop>
 </tabstops>
 <resources>
  <include location="../../../libs/vmisc/share/resources/theme.qrc"/>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>HistoryDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>HistoryDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
