/***************************************************************************
 *                                                                         *
 *   Copyright (C) 2017  Seamly, LLC                                       *
 *                                                                         *
 *   https://github.com/fashionfreedom/seamly2d                             *
 *                                                                         *
 ***************************************************************************
 **
 **  Seamly2D is free software: you can redistribute it and/or modify
 **  it under the terms of the GNU General Public License as published by
 **  the Free Software Foundation, either version 3 of the License, or
 **  (at your option) any later version.
 **
 **  Seamly2D is distributed in the hope that it will be useful,
 **  but WITHOUT ANY WARRANTY; without even the implied warranty of
 **  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 **  GNU General Public License for more details.
 **
 **  You should have received a copy of the GNU General Public License
 **  along with Seamly2D.  If not, see <http://www.gnu.org/licenses/>.
 **
 **************************************************************************

 ************************************************************************
 **
 **  @file   vsplinepoint.h
 **  <AUTHOR> Telezhynskyi <dismine(at)gmail.com>
 **  @date   November 15, 2013
 **
 **  @brief
 **  @copyright
 **  This source code is part of the Valentine project, a pattern making
 **  program, whose allow create and modeling patterns of clothing.
 **  Copyright (C) 2013-2015 Seamly2D project
 **  <https://github.com/fashionfreedom/seamly2d> All Rights Reserved.
 **
 **  Seamly2D is free software: you can redistribute it and/or modify
 **  it under the terms of the GNU General Public License as published by
 **  the Free Software Foundation, either version 3 of the License, or
 **  (at your option) any later version.
 **
 **  Seamly2D is distributed in the hope that it will be useful,
 **  but WITHOUT ANY WARRANTY; without even the implied warranty of
 **  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 **  GNU General Public License for more details.
 **
 **  You should have received a copy of the GNU General Public License
 **  along with Seamly2D.  If not, see <http://www.gnu.org/licenses/>.
 **
 *************************************************************************/

#ifndef VSPLINEPOINT_H
#define VSPLINEPOINT_H

#include <QMetaType>
#include <QSharedDataPointer>
#include <QString>
#include <QTypeInfo>
#include <QtGlobal>

#include "vpointf.h"

class VFSplinePointData;

/**
 * @brief The VFSplinePoint class keep information about point in spline path. Each point have two angles and two
 * coefficient. Point represent at the same time first and last point of a spline.
 */
class VFSplinePoint
{
public:
    VFSplinePoint();
    VFSplinePoint(const VPointF &pSpline, qreal kAsm1, qreal angle1, qreal kAsm2, qreal angle2);
    VFSplinePoint(const VFSplinePoint &point);
    ~VFSplinePoint();

    VFSplinePoint &operator=(const VFSplinePoint &point);
#ifdef Q_COMPILER_RVALUE_REFS
	VFSplinePoint &operator=(VFSplinePoint &&point) Q_DECL_NOTHROW;
#endif

	void Swap(VFSplinePoint &point) Q_DECL_NOTHROW;

    VPointF P() const;
    void    SetP(const VPointF &value);
    qreal   Angle1() const;
    void    SetAngle1(const qreal &value);
    void    SetAngle2(const qreal &value);
    qreal   Angle2() const;
    qreal   KAsm1() const;
    void    SetKAsm1(const qreal &value);
    qreal   KAsm2() const;
    void    SetKAsm2(const qreal &value);
protected:
    QSharedDataPointer<VFSplinePointData> d;
};

Q_DECLARE_METATYPE(VFSplinePoint)
Q_DECLARE_TYPEINFO(VFSplinePoint, Q_MOVABLE_TYPE);

class VSplinePointData;

/**
 * @brief The VSplinePoint class keep information about point in spline path. Each point have two angles and two
 * lengths. Point represent at the same time first and last point of a spline.
 */
class VSplinePoint
{
public:
    VSplinePoint();
    VSplinePoint(const VPointF &pSpline, qreal angle1, const QString &angle1F, qreal angle2, const QString &angle2F,
                 qreal length1, const QString &length1F, qreal length2, const QString &length2F);
    VSplinePoint(const VSplinePoint &point);
    ~VSplinePoint();

    VSplinePoint &operator=(const VSplinePoint &point);
#ifdef Q_COMPILER_RVALUE_REFS
	VSplinePoint &operator=(VSplinePoint &&point) Q_DECL_NOTHROW;
#endif

	void Swap(VSplinePoint &point) Q_DECL_NOTHROW;

    VPointF P() const;
    void    SetP(const VPointF &value);

    qreal   Angle1() const;
    QString Angle1Formula() const;
    void    SetAngle1(const qreal &value, const QString &angle1F);

    qreal   Angle2() const;
    QString Angle2Formula() const;
    void    SetAngle2(const qreal &value, const QString &angle2F);

    qreal   Length1() const;
    QString Length1Formula() const;
    void    SetLength1(const qreal &value, const QString &length1F);

    qreal   Length2() const;
    QString Length2Formula() const;
    void    SetLength2(const qreal &value, const QString &length2F);

    bool    IsMovable() const;
protected:
    QSharedDataPointer<VSplinePointData> d;
};

Q_DECLARE_METATYPE(VSplinePoint)
Q_DECLARE_TYPEINFO(VSplinePoint, Q_MOVABLE_TYPE);

#endif // VSPLINEPOINT_H
