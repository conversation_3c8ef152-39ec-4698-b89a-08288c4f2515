<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SeamlyWelcomeDialog</class>
 <widget class="QDialog" name="SeamlyWelcomeDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>650</width>
    <height>450</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>650</width>
    <height>450</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Welcome</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/logos/seamly_logo_32.png</normaloff>:/icon/logos/seamly_logo_32.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QLabel" name="seamlyme_banner_Label">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>70</width>
         <height>400</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap resource="../../../libs/vmisc/share/resources/icon.qrc">:/icon/logos/seamly2d_vertical.png</pixmap>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="bodyscan_GroupBox">
       <property name="font">
        <font>
         <pointsize>14</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="title">
        <string>Welcome to Seamly2D</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QFormLayout" name="formLayout">
          <item row="0" column="1">
           <widget class="QLabel" name="label_2">
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Please choose your preferred units, decimal separator, language, and selection sound. (You can change these later.)</string>
            </property>
            <property name="wordWrap">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="units_Label">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Units:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QComboBox" name="units_ComboBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="toolTip">
             <string>Sets the default units for a new measurement file.</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="seperastor_Label">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Decimal separator:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QCheckBox" name="separator_CheckBox">
            <property name="minimumSize">
             <size>
              <width>300</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="toolTip">
             <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Selects what decimal separator char to use.  When checked the separator for the user's locale is used.  When unchecked the period is used.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
            </property>
            <property name="text">
             <string notr="true">&lt; User locale &gt;</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="language_Label">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>GUI language:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QComboBox" name="language_ComboBox">
            <property name="minimumSize">
             <size>
              <width>300</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="toolTip">
             <string>Sets the language used for Seamly2D.</string>
            </property>
           </widget>
          </item>
          <item row="4" column="0">
           <widget class="QLabel" name="selectionSound_Label">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Sound:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <widget class="QComboBox" name="selectionSound_ComboBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="toolTip">
             <string>Sets the node selection click  sound.</string>
            </property>
            <property name="locale">
             <locale language="English" country="UnitedStates"/>
            </property>
            <property name="currentText">
             <string notr="true">silent</string>
            </property>
            <property name="placeholderText">
             <string notr="true"/>
            </property>
            <item>
             <property name="text">
              <string notr="true">silent</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">button_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">mouse1_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">mouse2_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">mouse3_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">camera_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">camera_buzz</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">clock_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">clock_beep</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">microwave_beep</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">coffeemaker_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">coffeemaker2_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">inspirstar_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">phone2_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">phone1_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">scissors_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">wallswitch_click</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string notr="true">buzz_click</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QCheckBox" name="doNotShow_CheckBox">
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;When checked the Welcome window will not be displayed.  You can change this setting in the Seamly2D preferences.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Do not show again</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDialogButtonBox" name="buttonBox">
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="autoFillBackground">
        <bool>true</bool>
       </property>
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="standardButtons">
        <set>QDialogButtonBox::Ok</set>
       </property>
       <property name="centerButtons">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections/>
</ui>
