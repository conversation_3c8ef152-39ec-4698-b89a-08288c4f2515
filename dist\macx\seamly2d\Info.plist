<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>CFBundleIconFile</key>
	<string>@ICON@</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>LSMinimumSystemVersion</key>
	<string>${MACOSX_DEPLOYMENT_TARGET}</string>
	<key>NSHumanReadableCopyright</key>
	<string>© 2013-2023, Seamly2D project</string>
	<key>CFBundleSignature</key>
	<string>@TYPEINFO@</string>
	<key>CFBundleExecutable</key>
	<string>@EXECUTABLE@</string>
	<key>CFBundleIdentifier</key>
	<string>org.seamly2dproject.@EXECUTABLE@</string>
	<!-- Start: Do not edit here, use scripts/version.sh to update -->
	<key>CFBundleShortVersionString</key><string>0.6.0</string>
	<key>CFBundleVersion</key><string>0.6.0</string>
	<!-- End: Do not edit here -->
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleTypeIconFile</key>
			<string>2d_file.icns</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.seamly2dproject.sm2d</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>Seamly2D pattern file</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleTypeIconFile</key>
			<string>individual_size_file.icns</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.seamly2dproject.smis</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>SeamlyMe individual measurements</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleTypeIconFile</key>
			<string>multi_size_file.icns</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>org.seamly2dproject.smms</string>
			</array>
			<key>CFBundleTypeName</key>
                        <string>SeamlyMe multisize measurements</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
		</dict>
	</array>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.text</string>
				<string>public.xml</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Seamly2D pattern file</string>
			<key>UTTypeIconFile</key>
			<string>pattern</string>
			<key>UTTypeIdentifier</key>
			<string>org.seamly2dproject.sm2d</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>sm2d</string>
				</array>
				<key>public.mime-type</key>
            	<string>text/xml</string>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.text</string>
				<string>public.xml</string>
			</array>
			<key>UTTypeDescription</key>
			<string>SeamlyMe individual measurements</string>
			<key>UTTypeIconFile</key>
			<string>individual_size_file.icns</string>
			<key>UTTypeIdentifier</key>
			<string>org.seamly2dproject.smis</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>smin</string>
				</array>
				<key>public.mime-type</key>
            	<string>text/xml</string>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.text</string>
				<string>public.xml</string>
			</array>
			<key>UTTypeDescription</key>
                        <string>SeamlyMe multisize measurements</string>
			<key>UTTypeIconFile</key>
			<string>multi_size_file.icns</string>
			<key>UTTypeIdentifier</key>
			<string>org.seamly2dproject.smms</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>smms</string>
				</array>
				<key>public.mime-type</key>
            	<string>text/xml</string>
			</dict>
		</dict>
	</array>
</dict>
</plist>
