<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>TMainWindow</class>
 <widget class="QMainWindow" name="TMainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>835</width>
    <height>782</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string notr="true"/>
  </property>
  <property name="windowIcon">
   <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/logos/seamlyme_logo_32.png</normaloff>:/icon/logos/seamlyme_logo_32.png</iconset>
  </property>
  <widget class="QWidget" name="centralWidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
     <horstretch>15</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QLabel" name="labelToolTip">
      <property name="sizePolicy">
       <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="text">
       <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:18pt;&quot;&gt;Select New for creation measurement file.&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="toolTip">
       <string/>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tabMeasurements">
       <attribute name="icon">
        <iconset resource="share/resources/seamlymeicon.qrc">
         <normaloff>:/seamlymeicon/16x16/measurement.png</normaloff>:/seamlymeicon/16x16/measurement.png</iconset>
       </attribute>
       <attribute name="title">
        <string>Measurements</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QToolButton" name="clipboard_ToolButton">
            <property name="toolTip">
             <string>Copy to clipboard</string>
            </property>
            <property name="text">
             <string notr="true">...</string>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
              <normaloff>:/icon/32x32/clipboard_icon.png</normaloff>:/icon/32x32/clipboard_icon.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="labelFind">
            <property name="text">
             <string>Find:</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="find_LineEdit">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="placeholderText">
             <string>Search</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QToolButton" name="toolButtonFindPrevious">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="toolTip">
             <string>Find Previous</string>
            </property>
            <property name="text">
             <string notr="true">...</string>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
              <normaloff>:/icons/win.icon.theme/16x16/actions/go-previous.png</normaloff>:/icons/win.icon.theme/16x16/actions/go-previous.png</iconset>
            </property>
            <property name="shortcut">
             <string>Ctrl+Shift+G</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QToolButton" name="toolButtonFindNext">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="toolTip">
             <string>Find Next</string>
            </property>
            <property name="text">
             <string notr="true">...</string>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
              <normaloff>:/icons/win.icon.theme/16x16/actions/go-next.png</normaloff>:/icons/win.icon.theme/16x16/actions/go-next.png</iconset>
            </property>
            <property name="shortcut">
             <string>Ctrl+G</string>
            </property>
            <property name="autoExclusive">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QToolButton" name="regex_ToolButton">
            <property name="toolTip">
             <string>Seach by regular expression</string>
            </property>
            <property name="text">
             <string notr="true"/>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
              <normaloff>:/icon/svg/regex.svg</normaloff>:/icon/svg/regex.svg</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>24</width>
              <height>18</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QToolButton" name="case_ToolButton">
            <property name="toolTip">
             <string>Case sensitive</string>
            </property>
            <property name="text">
             <string notr="true"/>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
              <normaloff>:/icon/svg/case.svg</normaloff>:/icon/svg/case.svg</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>24</width>
              <height>18</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QToolButton" name="word_ToolButton">
            <property name="toolTip">
             <string>Search by full word</string>
            </property>
            <property name="text">
             <string notr="true"/>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
              <normaloff>:/icon/svg/word.svg</normaloff>:/icon/svg/word.svg</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>24</width>
              <height>18</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QTableWidget" name="tableWidget">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>2</verstretch>
           </sizepolicy>
          </property>
          <property name="alternatingRowColors">
           <bool>true</bool>
          </property>
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
          <attribute name="horizontalHeaderStretchLastSection">
           <bool>true</bool>
          </attribute>
          <column>
           <property name="text">
            <string>Name</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Number</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Full name</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Calculated value</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Formula</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Base value</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>In sizes</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>In heights</string>
           </property>
          </column>
         </widget>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item alignment="Qt::AlignLeft">
           <widget class="QToolButton" name="toolButtonTop">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="toolTip">
             <string>Move measurement top</string>
            </property>
            <property name="text">
             <string notr="true">...</string>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
              <normaloff>:/icons/win.icon.theme/16x16/actions/go-top.png</normaloff>:/icons/win.icon.theme/16x16/actions/go-top.png</iconset>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignLeft">
           <widget class="QToolButton" name="toolButtonUp">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="toolTip">
             <string>Move measurement up</string>
            </property>
            <property name="text">
             <string notr="true">...</string>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
              <normaloff>:/icons/win.icon.theme/16x16/actions/go-up.png</normaloff>:/icons/win.icon.theme/16x16/actions/go-up.png</iconset>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignLeft">
           <widget class="QToolButton" name="toolButtonDown">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="toolTip">
             <string>Move measurement down</string>
            </property>
            <property name="text">
             <string notr="true">...</string>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
              <normaloff>:/icons/win.icon.theme/16x16/actions/go-down.png</normaloff>:/icons/win.icon.theme/16x16/actions/go-down.png</iconset>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignLeft">
           <widget class="QToolButton" name="toolButtonBottom">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="toolTip">
             <string>Move measurement bottom</string>
            </property>
            <property name="text">
             <string notr="true">...</string>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
              <normaloff>:/icons/win.icon.theme/16x16/actions/go-bottom.png</normaloff>:/icons/win.icon.theme/16x16/actions/go-bottom.png</iconset>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>5000</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QToolButton" name="toolButtonRemove">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="toolTip">
             <string>Delete measurement</string>
            </property>
            <property name="text">
             <string notr="true">...</string>
            </property>
            <property name="icon">
             <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
              <normaloff>:/icon/32x32/remove.png</normaloff>:/icon/32x32/remove.png</iconset>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBoxDetails">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>2</verstretch>
           </sizepolicy>
          </property>
          <property name="toolTip">
           <string>Details</string>
          </property>
          <property name="title">
           <string>Details</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <widget class="QLabel" name="label">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>Name:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditName">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="toolTip">
                <string>Measurement's name in a formula</string>
               </property>
               <property name="placeholderText">
                <string>Measurement's name in a formula.</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_9">
             <item>
              <widget class="QLabel" name="labelFullName">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>Full name:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEditFullName">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="minimumSize">
                <size>
                 <width>400</width>
                 <height>0</height>
                </size>
               </property>
               <property name="toolTip">
                <string/>
               </property>
               <property name="placeholderText">
                <string>Measurement's human-readable name.</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <item>
              <widget class="QLabel" name="labelCalculated">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>Calculated value:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="labelCalculatedValue">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>423</width>
                 <height>0</height>
                </size>
               </property>
               <property name="toolTip">
                <string>Calculated value</string>
               </property>
               <property name="text">
                <string notr="true"/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <item>
              <widget class="QLabel" name="labelFormula">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>Formula:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTop|Qt::AlignTrailing</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPlainTextEdit" name="plainTextEditFormula">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>64</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="tabChangesFocus">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_3">
               <item>
                <widget class="QToolButton" name="toolButtonExpr">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Function Wizard</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/seamlymeicon.qrc">
                   <normaloff>:/seamlymeicon/24x24/fx.png</normaloff>:/seamlymeicon/24x24/fx.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>24</width>
                   <height>24</height>
                  </size>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="verticalSpacer">
                 <property name="orientation">
                  <enum>Qt::Vertical</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>20</width>
                   <height>40</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <item>
              <widget class="QLabel" name="labelBaseValue">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>Base value:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QDoubleSpinBox" name="doubleSpinBoxBaseValue">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimum">
                <double>-10000.000000000000000</double>
               </property>
               <property name="maximum">
                <double>10000.000000000000000</double>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_7">
             <item>
              <widget class="QLabel" name="labelInSizes">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>In sizes:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QDoubleSpinBox" name="doubleSpinBoxInSizes">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimum">
                <double>-10000.000000000000000</double>
               </property>
               <property name="maximum">
                <double>10000.000000000000000</double>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_8">
             <item>
              <widget class="QLabel" name="labelInHeights">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>In heights:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QDoubleSpinBox" name="doubleSpinBoxInHeights">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimum">
                <double>-10000.000000000000000</double>
               </property>
               <property name="maximum">
                <double>10000.000000000000000</double>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_4">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_10">
             <item>
              <widget class="QLabel" name="label_7">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>Description:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTop|Qt::AlignTrailing</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPlainTextEdit" name="plainTextEditDescription">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>1</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>400</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>45</height>
                </size>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tabInformation">
       <attribute name="icon">
        <iconset resource="share/resources/seamlymeicon.qrc">
         <normaloff>:/seamlymeicon/16x16/info.png</normaloff>:/seamlymeicon/16x16/info.png</iconset>
       </attribute>
       <attribute name="title">
        <string>Information</string>
       </attribute>
       <layout class="QFormLayout" name="formLayout_2">
        <item row="0" column="0">
         <widget class="QLabel" name="label_21">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Type:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QLabel" name="labelMType">
          <property name="text">
           <string>Measurement type</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="label_9">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Path:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <layout class="QHBoxLayout" name="horizontalLayoutPath">
          <item>
           <widget class="QLineEdit" name="lineEditPathToFile">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">background: transparent;</string>
            </property>
            <property name="frame">
             <bool>false</bool>
            </property>
            <property name="readOnly">
             <bool>true</bool>
            </property>
            <property name="placeholderText">
             <string notr="true">Path to the measurement file</string>
            </property>
           </widget>
          </item>
          <item alignment="Qt::AlignRight">
           <widget class="QPushButton" name="pushButtonShowInExplorer">
            <property name="enabled">
             <bool>false</bool>
            </property>
            <property name="sizePolicy">
             <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="text">
             <string>Show in Explorer</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="labelPMSystem">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>PM system:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QComboBox" name="comboBoxPMSystem">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="MinimumExpanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="labelBaseSize">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Base size:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="labelBaseSizeValue">
          <property name="text">
           <string>Base size value</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QLabel" name="labelBaseHeight">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Base height:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QLabel" name="labelBaseHeightValue">
          <property name="text">
           <string>Base height value</string>
          </property>
         </widget>
        </item>
        <item row="5" column="0">
         <widget class="QLabel" name="labelGivenName">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Given name:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="5" column="1">
         <widget class="QLineEdit" name="lineEditGivenName">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="placeholderText">
           <string>Customer's name</string>
          </property>
         </widget>
        </item>
        <item row="6" column="0">
         <widget class="QLabel" name="labelFamilyName">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Family name:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="6" column="1">
         <widget class="QLineEdit" name="lineEditFamilyName">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="placeholderText">
           <string>Customer's family name</string>
          </property>
         </widget>
        </item>
        <item row="7" column="0">
         <widget class="QLabel" name="labelBirthDate">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Birth date:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="7" column="1">
         <widget class="QDateEdit" name="dateEditBirthDate">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="MinimumExpanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>124</width>
            <height>0</height>
           </size>
          </property>
          <property name="displayFormat">
           <string notr="true">yyyy-MM-dd</string>
          </property>
          <property name="calendarPopup">
           <bool>true</bool>
          </property>
          <property name="currentSectionIndex">
           <number>0</number>
          </property>
          <property name="date">
           <date>
            <year>1800</year>
            <month>1</month>
            <day>1</day>
           </date>
          </property>
         </widget>
        </item>
        <item row="8" column="0">
         <widget class="QLabel" name="labelGender">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Gender:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="8" column="1">
         <widget class="QComboBox" name="comboBoxGender">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="MinimumExpanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
         </widget>
        </item>
        <item row="9" column="0">
         <widget class="QLabel" name="labelEmail">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Email:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="9" column="1">
         <widget class="QLineEdit" name="lineEditEmail">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="placeholderText">
           <string>Customer's email address</string>
          </property>
         </widget>
        </item>
        <item row="10" column="0">
         <widget class="QLabel" name="labelNotes">
          <property name="minimumSize">
           <size>
            <width>130</width>
            <height>0</height>
           </size>
          </property>
          <property name="text">
           <string>Notes:</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTop|Qt::AlignTrailing</set>
          </property>
         </widget>
        </item>
        <item row="10" column="1">
         <widget class="QPlainTextEdit" name="plainTextEditNotes">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>1</verstretch>
           </sizepolicy>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>835</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <widget class="QMenu" name="import_Menu">
     <property name="title">
      <string>Import body scan as</string>
     </property>
     <property name="icon">
      <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
       <normaloff>:/icon/32x32/body_scan.png</normaloff>:/icon/32x32/body_scan.png</iconset>
     </property>
     <addaction name="bodyScanner2_Action"/>
    </widget>
    <addaction name="actionNew"/>
    <addaction name="separator"/>
    <addaction name="actionOpenIndividual"/>
    <addaction name="actionOpenMultisize"/>
    <addaction name="actionOpenTemplate"/>
    <addaction name="actionCreateFromExisting"/>
    <addaction name="separator"/>
    <addaction name="import_Menu"/>
    <addaction name="separator"/>
    <addaction name="print_Action"/>
    <addaction name="actionSave"/>
    <addaction name="actionSaveAs"/>
    <addaction name="actionExportToCSV"/>
    <addaction name="separator"/>
    <addaction name="actionReadOnly"/>
    <addaction name="separator"/>
    <addaction name="actionPreferences"/>
    <addaction name="separator"/>
    <addaction name="actionQuit"/>
    <addaction name="separator"/>
   </widget>
   <widget class="QMenu" name="window_Menu">
    <property name="title">
     <string>Window</string>
    </property>
   </widget>
   <widget class="QMenu" name="help_Menu">
    <property name="title">
     <string>Help</string>
    </property>
    <addaction name="shortcuts_Action"/>
    <addaction name="actionAboutQt"/>
    <addaction name="actionAboutSeamlyMe"/>
   </widget>
   <widget class="QMenu" name="measurements_Menu">
    <property name="title">
     <string>Measurements</string>
    </property>
    <addaction name="actionAddKnown"/>
    <addaction name="actionAddCustom"/>
    <addaction name="actionDatabase"/>
    <addaction name="actionImportFromPattern"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="measurements_Menu"/>
   <addaction name="window_Menu"/>
   <addaction name="help_Menu"/>
  </widget>
  <widget class="QToolBar" name="mainToolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="windowTitle">
    <string>Menu</string>
   </property>
   <property name="toolTip">
    <string/>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionNew"/>
   <addaction name="actionOpenTemplate"/>
   <addaction name="actionOpenIndividual"/>
   <addaction name="actionOpenMultisize"/>
   <addaction name="print_Action"/>
   <addaction name="actionSave"/>
   <addaction name="actionSaveAs"/>
   <addaction name="actionAddKnown"/>
   <addaction name="actionAddCustom"/>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
  <widget class="QToolBar" name="toolBarGradation">
   <property name="windowTitle">
    <string>Gradation</string>
   </property>
   <property name="movable">
    <bool>false</bool>
   </property>
   <property name="allowedAreas">
    <set>Qt::AllToolBarAreas</set>
   </property>
   <property name="floatable">
    <bool>false</bool>
   </property>
   <attribute name="toolBarArea">
    <enum>BottomToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
  </widget>
  <widget class="QDockWidget" name="dockWidgetDiagram">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
     <horstretch>1</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="features">
    <set>QDockWidget::DockWidgetClosable|QDockWidget::DockWidgetMovable</set>
   </property>
   <property name="allowedAreas">
    <set>Qt::LeftDockWidgetArea|Qt::RightDockWidgetArea</set>
   </property>
   <property name="windowTitle">
    <string>Measurement diagram</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>2</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents">
    <property name="sizePolicy">
     <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
      <horstretch>1</horstretch>
      <verstretch>0</verstretch>
     </sizepolicy>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout_4">
     <item alignment="Qt::AlignHCenter|Qt::AlignVCenter">
      <widget class="QLabel" name="labelDiagram">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="text">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-size:340pt;&quot;&gt;?&lt;/span&gt;&lt;/p&gt;&lt;p align=\&quot;center\&quot;&gt;Unknown measurement&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="scaledContents">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <action name="actionOpenIndividual">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/individual_size_file.png</normaloff>:/icon/32x32/individual_size_file.png</iconset>
   </property>
   <property name="text">
    <string>Open individual ...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+O</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionSave">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/32x32/actions/document-save.png</normaloff>:/icons/win.icon.theme/32x32/actions/document-save.png</iconset>
   </property>
   <property name="text">
    <string>Save</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionSaveAs">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/32x32/actions/document-save-as.png</normaloff>:/icons/win.icon.theme/32x32/actions/document-save-as.png</iconset>
   </property>
   <property name="text">
    <string>Save As ...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+S</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionQuit">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/16x16/actions/application-exit.png</normaloff>:/icons/win.icon.theme/16x16/actions/application-exit.png</iconset>
   </property>
   <property name="text">
    <string>Exit</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Q</string>
   </property>
   <property name="menuRole">
    <enum>QAction::QuitRole</enum>
   </property>
  </action>
  <action name="actionAboutQt">
   <property name="text">
    <string>About &amp;Qt</string>
   </property>
   <property name="menuRole">
    <enum>QAction::AboutQtRole</enum>
   </property>
  </action>
  <action name="actionAboutSeamlyMe">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/logos/seamlyme_logo_32.png</normaloff>:/icon/logos/seamlyme_logo_32.png</iconset>
   </property>
   <property name="text">
    <string>About SeamlyMe</string>
   </property>
   <property name="menuRole">
    <enum>QAction::AboutRole</enum>
   </property>
  </action>
  <action name="actionNew">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/32x32/actions/document-new.png</normaloff>:/icons/win.icon.theme/32x32/actions/document-new.png</iconset>
   </property>
   <property name="text">
    <string>New</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionAddKnown">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/seamlymeicon.qrc">
     <normaloff>:/seamlymeicon/24x24/red_plus.png</normaloff>:/seamlymeicon/24x24/red_plus.png</iconset>
   </property>
   <property name="text">
    <string>Add known</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionAddCustom">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/seamlymeicon.qrc">
     <normaloff>:/seamlymeicon/24x24/orange_plus.png</normaloff>:/seamlymeicon/24x24/orange_plus.png</iconset>
   </property>
   <property name="text">
    <string>Add custom</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionReadOnly">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/seamlymeicon.qrc">
     <normaloff>:/seamlymeicon/24x24/padlock_opened.png</normaloff>:/seamlymeicon/24x24/padlock_opened.png</iconset>
   </property>
   <property name="text">
    <string>Read only</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionOpenMultisize">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/multisize_size_file.png</normaloff>:/icon/32x32/multisize_size_file.png</iconset>
   </property>
   <property name="text">
    <string>Open multisize ...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+O</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionOpenTemplate">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/template_size_file.png</normaloff>:/icon/32x32/template_size_file.png</iconset>
   </property>
   <property name="text">
    <string>Open template ...</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Alt+O</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionDatabase">
   <property name="text">
    <string>Database</string>
   </property>
   <property name="toolTip">
    <string>Show information about all known measurement</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionPreferences">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/32x32/actions/preferences-other.png</normaloff>:/icons/win.icon.theme/32x32/actions/preferences-other.png</iconset>
   </property>
   <property name="text">
    <string>Preferences</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+,</string>
   </property>
   <property name="menuRole">
    <enum>QAction::PreferencesRole</enum>
   </property>
  </action>
  <action name="actionImportFromPattern">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Import from a pattern</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionCreateFromExisting">
   <property name="text">
    <string>Create from existing ...</string>
   </property>
   <property name="toolTip">
    <string>Create from existing file</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionExportToCSV">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Export to CSV</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+E</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="shortcuts_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/me_key_icon.png</normaloff>:/icon/32x32/me_key_icon.png</iconset>
   </property>
   <property name="text">
    <string>Shortcuts</string>
   </property>
   <property name="shortcut">
    <string>K</string>
   </property>
  </action>
  <action name="print_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/32x32/actions/document-print.png</normaloff>:/icons/win.icon.theme/32x32/actions/document-print.png</iconset>
   </property>
   <property name="text">
    <string>Print</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+P</string>
   </property>
  </action>
  <action name="bodyScanner1_Action">
   <property name="text">
    <string>3D Measure Up</string>
   </property>
  </action>
  <action name="bodyScanner2_Action">
   <property name="text">
    <string>3D Look</string>
   </property>
  </action>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="share/resources/seamlymeicon.qrc"/>
  <include location="../../libs/vmisc/share/resources/theme.qrc"/>
  <include location="../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections/>
</ui>
