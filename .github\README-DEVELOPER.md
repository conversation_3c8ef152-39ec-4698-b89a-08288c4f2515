# Build Seamly2D

## Basic Software Prerequisites:  
* [Qt 5.15.2](https://www.qt.io/download-open-source) (includes Qt, QtCreator, QtChooser, and Qt Maintenance Tool)
* [Git](https://git-scm.com/downloads) or [Github Desktop for Windows and MacOS](https://desktop.github.com)
* Compiler - MSVC 2022, gcc, and g++ are included with QtCreator, and you can add or update them using the Qt Maintenance Tool (Maintenance.exe).
* Pdftops (from XpdfReader or poppler) - Required to create PS or EPS layout files.
* Check the sections below for your operating system to find additional installation requirements.

## Code Documentation
   * [Doxygen Docs](https://fashionfreedom.github.io/Seamly2D/)

## Development methods and styles:
   * [GitHub Flow workflow](https://githubflow.github.io)
   * [Gibhub commit message style guide](https://www.conventionalcommits.org/en/v1.0.0/)
   * [Github issue description style guide](https://guides.github.com/features/issues/)
   * Read more about code styles and developer items of interest on our [Developer wiki](https://github.com/FashionFreedom/Seamly2D/wiki).

## Build method:
   * Review our [GitHub Action CI script](workflows/ci.yml).

## Tools for improved Code reviews, Pull Requests, and Commits:
   * [Codesee](https://codesee.io), autogenerated maps of code base and PRs.  SeamlyTeam contributors can access our map [here](https://app.codesee.io/maps/5fcdb0b0-ffe6-11ed-bb97-c168054a9020)
   * [Google Blocks](https://blocks.githubnext.com/fashionfreedom/seamly2d/blob/develop?blockKey=githubnext__blocks-examples__minimap), drill-down tool to visually organize and analyze the code base.
   * [What the Diff](https://github.com/apps/what-the-diff), an AI-powered assistant that adds expanded comments to commits.

___________________________________________________
## Build on Linux

  _These instructions apply in general_

* Install Qt 5.15.2, eg via [Qt unified installer](https://www.qt.io/download-qt-installer) or https://github.com/miurahr/aqtinstall
* Install QtCreator https://wiki.qt.io/VendorPackages
* Install Additional libraries
  - gnu compiler
  - poppler (pdftops)
  - Example for Ubuntu 22.04:
    ```
    $ sudo apt install -y build-essential git poppler-tools
    ```
* Build and install:  
  ```
  $ qmake
  $ make -j$(nproc)
  $ sudo make install
  ```
  _Note: The default prefix for command `make install` is `/usr`.  To define another prefix, install with an INSTALL_ROOT specified. This example sets `/usr/local` as the new prefix for Seamly's installation binary files:_
    ```
    $ sudo make INSTALL_ROOT=/usr/local install
    ```
* Copy pdftops to Seamly build directory if you need to create post script (.ps and .eps) pattern piece layouts.

## Build on MacOSX and Windows 10/11
1. MacOS only:
    * Read about Qt for macOS [here](https://doc.qt.io/qt-5/macos.html)
    * Install [Xcode 11](https://developer.apple.com/download/all/)
    * Setup/validate build environment. Read more [here](https://doc.qt.io/qt-5/macos.html#build-environment).
      * Switch to Xcode: `sudo xcode-select --switch /Applications/Xcode.app`
      * Validate clang compiler points to Xcode: `xcrun -sdk macosx -find clang`
      * Validate SDK version (macOS 10.15): `xcrun -sdk macosx --show-sdk-path`
    * Install Xpdf: `sudo port install xpdf`

2. Both MacOS and Windows:
    * Download & run the [Qt unified installer](https://www.qt.io/download-qt-installer). Create a Qt account for open source Community Edition if you don't have one.  
      - Select:
        * Custom Installation
        * Qt  - _Minimize your options, otherwise your download size could be in Gs_
          * Qt 5.15.2
            * MSVC 2019
            * Qt Debug Information Files
          * Developer and Designer Tools
            * Qt Creator
            * Qt Creator CDB Debugger Support
            * Debugging Tools for Windows
            * Qt Creator Debug Symbols
            (Qt Maintenance Tool is always installed with Developer & Designer Tools - this will be highlighted)

3. Windows only:
    * Download [XpdfReader](http://www.xpdfreader.com/download.html) for Windows. Extract to `C:/Program Files`. Rename folder to `C:/Program Files/Xpdf`.
    * Read about Qt for Windows [here](https://doc.qt.io/qt-5/windows.html).
    * Add Qt and QtCreator directories to the Windows PATH environment variable through Control Panel:  
      `[Control Panel | System And Security | System | Advanced Tab | Environment Variables button]`

4. Mac only for signing and notarizing:
    * Enable signing and notarizing at qmake step:
      ```
      qmake Seamly2D.pro CONFIG+=macSign
      ```

5. Both MacOS and Windows:
    * Build the Seamly2D project
      * To build with Qt's *QtCreator* IDE:
        * Create your compiler kit.  Read more about adding compilers [on the Qt website](https://doc.qt.io/qtcreator/creator-tool-chains.html).  
        * Complete your build settings.  Read more about build settings [here](https://doc.qt.io/qtcreator/creator-build-settings.html).
        * Open the Seamly2D project with 'File > Open File or Project'. Navigate to the 'seamly2d/src' directory and select 'Seamly2D.pro'.
        * Open the Configure Project tab and select your compiler kit. Read more [here](https://doc.qt.io/qtcreator/creator-project-opening.html).      
        * Build with the 'Build and Run Kit Selector' icon, or use 'Build' and 'Run' from the Tools menu. Read more [here](https://doc.qt.io/qtcreator/creator-building-targets.html).
      * To build with Qt's *qmake* from a terminal window:  
        * Read more about jom [here](https://wiki.qt.io/Jom)
        * Read more about nmake [here](https://learn.microsoft.com/en-us/cpp/build/reference/nmake-reference?view=msvc-170)
          ```
          cd $SOURCE_DIRECTORY\build
          qmake ..\Seamly2D.pro
          nmake      # (or jom. Assign multiple CPUs to speed up compilation time but don't use all - leave at least one CPU for your OS.)
          ```
      * Copy the `C:\Program Files\Xpdf\bin64\pdftops.exe` (or bin32) file to the Seamly build directory to enable creation of post script (.ps and .eps) pattern piece layouts.
