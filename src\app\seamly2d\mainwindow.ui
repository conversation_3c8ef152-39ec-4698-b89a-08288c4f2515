<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1100</width>
    <height>809</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Seamly2D</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/logos/seamly_logo_32.png</normaloff>:/icon/logos/seamly_logo_32.png</iconset>
  </property>
  <property name="locale">
   <locale language="Saho" country="Eritrea"/>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <property name="leftMargin">
     <number>4</number>
    </property>
    <property name="topMargin">
     <number>4</number>
    </property>
    <property name="rightMargin">
     <number>4</number>
    </property>
    <property name="bottomMargin">
     <number>4</number>
    </property>
    <item>
     <widget class="VMainGraphicsView" name="view">
      <property name="enabled">
       <bool>true</bool>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>12</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="verticalScrollBarPolicy">
       <enum>Qt::ScrollBarAlwaysOn</enum>
      </property>
      <property name="horizontalScrollBarPolicy">
       <enum>Qt::ScrollBarAlwaysOn</enum>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1100</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="file_Menu">
    <property name="title">
     <string>&amp;File</string>
    </property>
    <widget class="QMenu" name="print_Menu">
     <property name="title">
      <string>Print</string>
     </property>
     <property name="icon">
      <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
       <normaloff>:/icons/win.icon.theme/32x32/actions/document-print.png</normaloff>:/icons/win.icon.theme/32x32/actions/document-print.png</iconset>
     </property>
     <addaction name="print_Action"/>
     <addaction name="printTiled_Action"/>
    </widget>
    <widget class="QMenu" name="preview_Menu">
     <property name="title">
      <string>Preview</string>
     </property>
     <property name="icon">
      <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
       <normaloff>:/icons/win.icon.theme/32x32/actions/document-print-preview.png</normaloff>:/icons/win.icon.theme/32x32/actions/document-print-preview.png</iconset>
     </property>
     <addaction name="printPreview_Action"/>
     <addaction name="printPreviewTiled_Action"/>
    </widget>
    <addaction name="actionNew"/>
    <addaction name="actionOpen"/>
    <addaction name="closePattern_Action"/>
    <addaction name="save_Action"/>
    <addaction name="saveAs_Action"/>
    <addaction name="separator"/>
    <addaction name="print_Menu"/>
    <addaction name="preview_Menu"/>
    <addaction name="separator"/>
    <addaction name="exportAs_Action"/>
    <addaction name="separator"/>
    <addaction name="appPreferences_Action"/>
    <addaction name="patternPreferences_Action"/>
    <addaction name="separator"/>
    <addaction name="labelTemplateEditor_Action"/>
    <addaction name="separator"/>
    <addaction name="documentInfo_Action"/>
    <addaction name="separator"/>
    <addaction name="exit_Action"/>
   </widget>
   <widget class="QMenu" name="help_Menu">
    <property name="title">
     <string>&amp;Help</string>
    </property>
    <addaction name="shortcuts_Action"/>
    <addaction name="wiki_Action"/>
    <addaction name="forum_Action"/>
    <addaction name="reportBug_Action"/>
    <addaction name="aboutQt_Action"/>
    <addaction name="aboutSeamly2D_Action"/>
   </widget>
   <widget class="QMenu" name="measurements_Menu">
    <property name="title">
     <string>Measurements</string>
    </property>
    <addaction name="openSeamlyMe_Action"/>
    <addaction name="editCurrent_Action"/>
    <addaction name="unloadMeasurements_Action"/>
    <addaction name="loadIndividual_Action"/>
    <addaction name="loadMultisize_Action"/>
    <addaction name="syncMeasurements_Action"/>
    <addaction name="table_Action"/>
    <addaction name="exportVariablesToCSV_Action"/>
   </widget>
   <widget class="QMenu" name="history_Menu">
    <property name="title">
     <string>History</string>
    </property>
    <addaction name="history_Action"/>
   </widget>
   <widget class="QMenu" name="view_Menu">
    <property name="title">
     <string>View</string>
    </property>
    <widget class="QMenu" name="pointNames_Menu">
     <property name="title">
      <string>Point Names</string>
     </property>
     <addaction name="showPointNames_Action"/>
     <addaction name="increaseSize_Action"/>
     <addaction name="decreaseSize_Action"/>
     <addaction name="useToolColor_Action"/>
    </widget>
    <addaction name="showDraftMode"/>
    <addaction name="pieceMode_Action"/>
    <addaction name="layoutMode_Action"/>
    <addaction name="separator"/>
    <addaction name="zoomIn_Action"/>
    <addaction name="zoomOut_Action"/>
    <addaction name="zoom100Percent_Action"/>
    <addaction name="zoomToFit_Action"/>
    <addaction name="zoomToPrevious_Action"/>
    <addaction name="zoomToSelected_Action"/>
    <addaction name="zoomToArea_Action"/>
    <addaction name="zoomPan_Action"/>
    <addaction name="zoomToPoint_Action"/>
    <addaction name="separator"/>
    <addaction name="pointNames_Menu"/>
    <addaction name="toggleWireframe_Action"/>
    <addaction name="toggleControlPoints_Action"/>
    <addaction name="toggleAxisOrigin_Action"/>
    <addaction name="toggleSeamAllowances_Action"/>
    <addaction name="toggleGrainLines_Action"/>
    <addaction name="toggleLabels_Action"/>
    <addaction name="separator"/>
   </widget>
   <widget class="QMenu" name="edit_Menu">
    <property name="title">
     <string>Edit</string>
    </property>
    <addaction name="separator"/>
    <addaction name="previousDraftBlock_Action"/>
    <addaction name="nextDraftBlock_Action"/>
   </widget>
   <widget class="QMenu" name="tools_Menu">
    <property name="title">
     <string>&amp;Tools</string>
    </property>
    <widget class="QMenu" name="operations_Menu">
     <property name="title">
      <string>&amp;Operations</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/operations_toolbox.png</normaloff>:/toolicon/32x32/operations_toolbox.png</iconset>
     </property>
     <addaction name="group_Action"/>
     <addaction name="rotation_Action"/>
     <addaction name="mirrorByLine_Action"/>
     <addaction name="mirrorByAxis_Action"/>
     <addaction name="move_Action"/>
     <addaction name="trueDarts_Action"/>
     <addaction name="exportDraftBlocks_Action"/>
    </widget>
    <widget class="QMenu" name="points_Menu">
     <property name="title">
      <string>Point</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/points_toolbox.png</normaloff>:/toolicon/32x32/points_toolbox.png</iconset>
     </property>
     <addaction name="pointAtDistanceAngle_Action"/>
     <addaction name="pointAlongLine_Action"/>
     <addaction name="midpoint_Action"/>
     <addaction name="pointAlongPerpendicular_Action"/>
     <addaction name="bisector_Action"/>
     <addaction name="pointOnShoulder_Action"/>
     <addaction name="pointOfContact_Action"/>
     <addaction name="triangle_Action"/>
     <addaction name="pointIntersectXY_Action"/>
     <addaction name="perpendicularPoint_Action"/>
     <addaction name="pointIntersectAxis_Action"/>
    </widget>
    <widget class="QMenu" name="details_Menu">
     <property name="title">
      <string>Details</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/details_toolbox.png</normaloff>:/toolicon/32x32/details_toolbox.png</iconset>
     </property>
     <addaction name="union_Action"/>
     <addaction name="exportPieces_Action"/>
    </widget>
    <widget class="QMenu" name="lines_Menu">
     <property name="title">
      <string>Line</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/lines_toolbox.png</normaloff>:/toolicon/32x32/lines_toolbox.png</iconset>
     </property>
     <addaction name="lineTool_Action"/>
     <addaction name="lineIntersect_Action"/>
    </widget>
    <widget class="QMenu" name="curves_Menu">
     <property name="title">
      <string>Curve</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/spline_toolboxs.png</normaloff>:/toolicon/32x32/spline_toolboxs.png</iconset>
     </property>
     <addaction name="curve_Action"/>
     <addaction name="spline_Action"/>
     <addaction name="curveWithCPs_Action"/>
     <addaction name="splineWithCPs_Action"/>
     <addaction name="pointAlongCurve_Action"/>
     <addaction name="pointAlongSpline_Action"/>
     <addaction name="curveIntersectCurve_Action"/>
     <addaction name="splineIntersectAxis_Action"/>
    </widget>
    <widget class="QMenu" name="arcs_Menu">
     <property name="title">
      <string>Arc</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/arcs_toolbox.png</normaloff>:/toolicon/32x32/arcs_toolbox.png</iconset>
     </property>
     <addaction name="arcTool_Action"/>
     <addaction name="pointAlongArc_Action"/>
     <addaction name="arcIntersectAxis_Action"/>
     <addaction name="arcIntersectArc_Action"/>
     <addaction name="circleIntersect_Action"/>
     <addaction name="circleTangent_Action"/>
     <addaction name="arcTangent_Action"/>
     <addaction name="arcWithLength_Action"/>
     <addaction name="ellipticalArc_Action"/>
    </widget>
    <widget class="QMenu" name="piece_Menu">
     <property name="title">
      <string>Piece</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/patternpiece_toolbox.png</normaloff>:/toolicon/32x32/patternpiece_toolbox.png</iconset>
     </property>
     <addaction name="addPiece_Action"/>
     <addaction name="anchorPoint_Action"/>
     <addaction name="internalPath_Action"/>
     <addaction name="insertNodes_Action"/>
    </widget>
    <widget class="QMenu" name="layouts_Menu">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="title">
      <string>Layout</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/layout_toolbox.png</normaloff>:/toolicon/32x32/layout_toolbox.png</iconset>
     </property>
     <addaction name="newPrintLayout_Action"/>
     <addaction name="exportLayout_Action"/>
    </widget>
    <widget class="QMenu" name="images_Menu">
     <property name="title">
      <string>Images</string>
     </property>
     <property name="icon">
      <iconset resource="share/resources/toolicon.qrc">
       <normaloff>:/toolicon/32x32/image_menu_icon.png</normaloff>:/toolicon/32x32/image_menu_icon.png</iconset>
     </property>
     <addaction name="importImage_Action"/>
    </widget>
    <addaction name="newDraft_Action"/>
    <addaction name="renameDraft_Action"/>
    <addaction name="separator"/>
    <addaction name="points_Menu"/>
    <addaction name="lines_Menu"/>
    <addaction name="curves_Menu"/>
    <addaction name="arcs_Menu"/>
    <addaction name="operations_Menu"/>
    <addaction name="piece_Menu"/>
    <addaction name="images_Menu"/>
    <addaction name="details_Menu"/>
    <addaction name="layouts_Menu"/>
    <addaction name="separator"/>
    <addaction name="lastTool_Action"/>
   </widget>
   <widget class="QMenu" name="menuUtiliries">
    <property name="title">
     <string>Utilities</string>
    </property>
    <addaction name="calculator_Action"/>
    <addaction name="decimalChart_Action"/>
   </widget>
   <addaction name="file_Menu"/>
   <addaction name="edit_Menu"/>
   <addaction name="view_Menu"/>
   <addaction name="tools_Menu"/>
   <addaction name="measurements_Menu"/>
   <addaction name="history_Menu"/>
   <addaction name="menuUtiliries"/>
   <addaction name="help_Menu"/>
  </widget>
  <widget class="QToolBar" name="file_ToolBar">
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="windowTitle">
    <string>File Toolbar</string>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionNew"/>
   <addaction name="actionOpen"/>
   <addaction name="save_Action"/>
   <addaction name="syncMeasurements_Action"/>
  </widget>
  <widget class="QToolBar" name="mode_ToolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="windowTitle">
    <string>Mode ToolBar</string>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="showDraftMode"/>
   <addaction name="pieceMode_Action"/>
   <addaction name="layoutMode_Action"/>
  </widget>
  <widget class="QToolBar" name="draft_ToolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="windowTitle">
    <string>Pattern Toolbar</string>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="newDraft_Action"/>
  </widget>
  <widget class="QToolBar" name="edit_Toolbar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
   <property name="windowTitle">
    <string>Edit Toolbar</string>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
  </widget>
  <widget class="QDockWidget" name="toolProperties_DockWidget">
   <property name="minimumSize">
    <size>
     <width>220</width>
     <height>70</height>
    </size>
   </property>
   <property name="windowTitle">
    <string>Property Editor</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>2</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents_10"/>
  </widget>
  <widget class="QDockWidget" name="layoutPages_DockWidget">
   <property name="minimumSize">
    <size>
     <width>220</width>
     <height>127</height>
    </size>
   </property>
   <property name="allowedAreas">
    <set>Qt::RightDockWidgetArea</set>
   </property>
   <property name="windowTitle">
    <string>Layout Pages</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>2</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents">
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>4</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>4</number>
     </property>
     <item>
      <widget class="QListWidget" name="listWidget">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>220</width>
         <height>0</height>
        </size>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Sunken</enum>
       </property>
       <property name="tabKeyNavigation">
        <bool>true</bool>
       </property>
       <property name="showDropIndicator" stdset="0">
        <bool>false</bool>
       </property>
       <property name="iconSize">
        <size>
         <width>150</width>
         <height>200</height>
        </size>
       </property>
       <property name="movement">
        <enum>QListView::Static</enum>
       </property>
       <property name="flow">
        <enum>QListView::TopToBottom</enum>
       </property>
       <property name="isWrapping" stdset="0">
        <bool>false</bool>
       </property>
       <property name="resizeMode">
        <enum>QListView::Adjust</enum>
       </property>
       <property name="spacing">
        <number>10</number>
       </property>
       <property name="viewMode">
        <enum>QListView::IconMode</enum>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QDockWidget" name="groups_DockWidget">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="minimumSize">
    <size>
     <width>220</width>
     <height>46</height>
    </size>
   </property>
   <property name="toolTip">
    <string/>
   </property>
   <property name="windowTitle">
    <string>Group Manager</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>2</number>
   </attribute>
   <widget class="QWidget" name="dockWidgetContents_2"/>
  </widget>
  <widget class="QStatusBar" name="statusBar"/>
  <widget class="QToolBar" name="zoom_ToolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="windowTitle">
    <string>Zoom ToolBar</string>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="zoomIn_Action"/>
   <addaction name="zoomOut_Action"/>
   <addaction name="zoom100Percent_Action"/>
   <addaction name="zoomToFit_Action"/>
   <addaction name="zoomToPrevious_Action"/>
   <addaction name="zoomToSelected_Action"/>
   <addaction name="zoomToArea_Action"/>
   <addaction name="zoomPan_Action"/>
   <addaction name="zoomToPoint_Action"/>
  </widget>
  <widget class="QToolBar" name="tools_ToolBox_ToolBar">
   <property name="contextMenuPolicy">
    <enum>Qt::DefaultContextMenu</enum>
   </property>
   <property name="windowTitle">
    <string>Toolbox ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>true</bool>
   </attribute>
   <addaction name="arrow_Action"/>
   <addaction name="points_Action"/>
   <addaction name="lines_Action"/>
   <addaction name="curves_Action"/>
   <addaction name="arcs_Action"/>
   <addaction name="modifications_Action"/>
   <addaction name="pieces_Action"/>
   <addaction name="details_Action"/>
   <addaction name="layout_Action"/>
   <addaction name="images_Action"/>
  </widget>
  <widget class="QToolBar" name="points_ToolBar">
   <property name="windowTitle">
    <string>Points Toolbar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="pointAtDistanceAngle_Action"/>
   <addaction name="pointAlongLine_Action"/>
   <addaction name="midpoint_Action"/>
   <addaction name="pointAlongPerpendicular_Action"/>
   <addaction name="bisector_Action"/>
   <addaction name="pointOnShoulder_Action"/>
   <addaction name="pointOfContact_Action"/>
   <addaction name="triangle_Action"/>
   <addaction name="pointIntersectXY_Action"/>
   <addaction name="perpendicularPoint_Action"/>
   <addaction name="pointIntersectAxis_Action"/>
  </widget>
  <widget class="QToolBar" name="lines_ToolBar">
   <property name="windowTitle">
    <string>Lines ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="lineTool_Action"/>
   <addaction name="lineIntersect_Action"/>
  </widget>
  <widget class="QToolBar" name="curves_ToolBar">
   <property name="windowTitle">
    <string>Curves ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="curve_Action"/>
   <addaction name="spline_Action"/>
   <addaction name="curveWithCPs_Action"/>
   <addaction name="splineWithCPs_Action"/>
   <addaction name="pointAlongCurve_Action"/>
   <addaction name="pointAlongSpline_Action"/>
   <addaction name="curveIntersectCurve_Action"/>
   <addaction name="splineIntersectAxis_Action"/>
  </widget>
  <widget class="QToolBar" name="arcs_ToolBar">
   <property name="windowTitle">
    <string>Arcs ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>true</bool>
   </attribute>
   <addaction name="arcTool_Action"/>
   <addaction name="pointAlongArc_Action"/>
   <addaction name="arcIntersectAxis_Action"/>
   <addaction name="arcIntersectArc_Action"/>
   <addaction name="circleIntersect_Action"/>
   <addaction name="circleTangent_Action"/>
   <addaction name="arcTangent_Action"/>
   <addaction name="arcWithLength_Action"/>
   <addaction name="ellipticalArc_Action"/>
  </widget>
  <widget class="QToolBar" name="operations_ToolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="windowTitle">
    <string>Operations ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="group_Action"/>
   <addaction name="rotation_Action"/>
   <addaction name="mirrorByLine_Action"/>
   <addaction name="mirrorByAxis_Action"/>
   <addaction name="move_Action"/>
   <addaction name="trueDarts_Action"/>
   <addaction name="exportDraftBlocks_Action"/>
  </widget>
  <widget class="QToolBar" name="pieces_ToolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="windowTitle">
    <string>Piece ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="addPiece_Action"/>
   <addaction name="anchorPoint_Action"/>
   <addaction name="insertNodes_Action"/>
   <addaction name="internalPath_Action"/>
  </widget>
  <widget class="QToolBar" name="details_ToolBar">
   <property name="windowTitle">
    <string>Details ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="union_Action"/>
   <addaction name="exportPieces_Action"/>
  </widget>
  <widget class="QToolBar" name="layout_ToolBar">
   <property name="windowTitle">
    <string>Layout ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="newPrintLayout_Action"/>
   <addaction name="exportLayout_Action"/>
  </widget>
  <widget class="QToolBar" name="pointName_ToolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="windowTitle">
    <string>Point Name ToolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>true</bool>
   </attribute>
   <addaction name="showPointNames_Action"/>
   <addaction name="increaseSize_Action"/>
   <addaction name="decreaseSize_Action"/>
   <addaction name="useToolColor_Action"/>
  </widget>
  <widget class="QDockWidget" name="toolbox_DockWidget">
   <property name="minimumSize">
    <size>
     <width>158</width>
     <height>530</height>
    </size>
   </property>
   <property name="maximumSize">
    <size>
     <width>524287</width>
     <height>524287</height>
    </size>
   </property>
   <property name="floating">
    <bool>false</bool>
   </property>
   <property name="allowedAreas">
    <set>Qt::LeftDockWidgetArea|Qt::RightDockWidgetArea</set>
   </property>
   <property name="windowTitle">
    <string>Toolbox</string>
   </property>
   <attribute name="dockWidgetArea">
    <number>1</number>
   </attribute>
   <widget class="QWidget" name="toolboxDockContents">
    <property name="minimumSize">
     <size>
      <width>0</width>
      <height>500</height>
     </size>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="leftMargin">
        <number>6</number>
       </property>
       <property name="topMargin">
        <number>6</number>
       </property>
       <property name="rightMargin">
        <number>6</number>
       </property>
       <property name="bottomMargin">
        <number>6</number>
       </property>
       <item>
        <widget class="QToolButton" name="arrowPointer_ToolButton">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="toolTip">
          <string>Tool pointer</string>
         </property>
         <property name="text">
          <string notr="true">...</string>
         </property>
         <property name="icon">
          <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/32x32/arrow_cursor.png</normaloff>:/icon/32x32/arrow_cursor.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>32</width>
           <height>32</height>
          </size>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item alignment="Qt::AlignTop">
        <widget class="QStackedWidget" name="toolbox_StackedWidget">
         <property name="minimumSize">
          <size>
           <width>124</width>
           <height>0</height>
          </size>
         </property>
         <property name="currentIndex">
          <number>0</number>
         </property>
         <widget class="QWidget" name="draftMode_Page">
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <widget class="QToolBox" name="draft_ToolBox">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
               <horstretch>1</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>124</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="toolTip">
              <string/>
             </property>
             <property name="currentIndex">
              <number>5</number>
             </property>
             <property name="tabSpacing">
              <number>4</number>
             </property>
             <widget class="QWidget" name="points_Page">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>128</width>
                <height>288</height>
               </rect>
              </property>
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>118</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="toolTip">
               <string>Tools for creating points.</string>
              </property>
              <attribute name="icon">
               <iconset resource="share/resources/toolicon.qrc">
                <normaloff>:/toolicon/32x32/points_toolbox.png</normaloff>:/toolicon/32x32/points_toolbox.png</iconset>
              </attribute>
              <attribute name="label">
               <string>Point</string>
              </attribute>
              <attribute name="toolTip">
               <string>Point</string>
              </attribute>
              <layout class="QFormLayout" name="formLayout_6">
               <property name="fieldGrowthPolicy">
                <enum>QFormLayout::AllNonFixedFieldsGrow</enum>
               </property>
               <property name="verticalSpacing">
                <number>4</number>
               </property>
               <property name="leftMargin">
                <number>8</number>
               </property>
               <property name="topMargin">
                <number>8</number>
               </property>
               <property name="rightMargin">
                <number>8</number>
               </property>
               <property name="bottomMargin">
                <number>8</number>
               </property>
               <item row="4" column="1">
                <widget class="QToolButton" name="bisector_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - On Bisector (O, B)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/bisector.png</normaloff>:/toolicon/32x32/bisector.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="7" column="0">
                <widget class="QToolButton" name="shoulderPoint_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Length to Line (P, S)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/shoulder.png</normaloff>:/toolicon/32x32/shoulder.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="7" column="1">
                <widget class="QToolButton" name="pointOfContact_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Arc and Line (A, L)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/point_intersect_arc_line.png</normaloff>:/toolicon/32x32/point_intersect_arc_line.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="9" column="0">
                <widget class="QToolButton" name="triangle_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Axis and Triangle (X, T)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/triangle.png</normaloff>:/toolicon/32x32/triangle.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="9" column="1">
                <widget class="QToolButton" name="pointIntersectXY_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect XY (X, Y)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/point_intersectxy_icon.png</normaloff>:/toolicon/32x32/point_intersectxy_icon.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="11" column="0">
                <widget class="QToolButton" name="height_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Line and Perpendicular (L, P)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/height.png</normaloff>:/toolicon/32x32/height.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="11" column="1">
                <widget class="QToolButton" name="lineIntersectAxis_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Line and Axis (L, X)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/line_intersect_axis.png</normaloff>:/toolicon/32x32/line_intersect_axis.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="4" column="0">
                <widget class="QToolButton" name="normal_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - On Perpendicular (O, P)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/normal.png</normaloff>:/toolicon/32x32/normal.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="0">
                <widget class="QToolButton" name="pointAtDistanceAngle_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Length and Angle (L, A)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/segment.png</normaloff>:/toolicon/32x32/segment.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QToolButton" name="alongLine_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - On Line (O, L)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/along_line.png</normaloff>:/toolicon/32x32/along_line.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QToolButton" name="midpoint_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Midpoint on Line (Shift+O, Shift+L)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/midpoint.png</normaloff>:/toolicon/32x32/midpoint.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="lines_Page">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>128</width>
                <height>58</height>
               </rect>
              </property>
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="toolTip">
               <string>Tools for creating lines.</string>
              </property>
              <attribute name="icon">
               <iconset resource="share/resources/toolicon.qrc">
                <normaloff>:/toolicon/32x32/lines_toolbox.png</normaloff>:/toolicon/32x32/lines_toolbox.png</iconset>
              </attribute>
              <attribute name="label">
               <string>Line</string>
              </attribute>
              <attribute name="toolTip">
               <string>Line</string>
              </attribute>
              <layout class="QFormLayout" name="formLayout_8">
               <property name="verticalSpacing">
                <number>4</number>
               </property>
               <property name="leftMargin">
                <number>8</number>
               </property>
               <property name="topMargin">
                <number>8</number>
               </property>
               <property name="rightMargin">
                <number>8</number>
               </property>
               <property name="bottomMargin">
                <number>8</number>
               </property>
               <item row="1" column="0">
                <widget class="QToolButton" name="line_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Line between 2 Points (Alt+L)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/line.png</normaloff>:/toolicon/32x32/line.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QToolButton" name="lineIntersect_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Lines (I, L)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/intersect.png</normaloff>:/toolicon/32x32/intersect.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="curves_Page">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>128</width>
                <height>196</height>
               </rect>
              </property>
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>118</width>
                <height>0</height>
               </size>
              </property>
              <property name="toolTip">
               <string>Tools for creating curves.</string>
              </property>
              <attribute name="icon">
               <iconset resource="share/resources/toolicon.qrc">
                <normaloff>:/toolicon/32x32/spline_toolboxs.png</normaloff>:/toolicon/32x32/spline_toolboxs.png</iconset>
              </attribute>
              <attribute name="label">
               <string>Curve</string>
              </attribute>
              <attribute name="toolTip">
               <string>Curve</string>
              </attribute>
              <layout class="QFormLayout" name="formLayout_10">
               <property name="verticalSpacing">
                <number>4</number>
               </property>
               <property name="leftMargin">
                <number>8</number>
               </property>
               <property name="topMargin">
                <number>8</number>
               </property>
               <property name="rightMargin">
                <number>8</number>
               </property>
               <property name="bottomMargin">
                <number>8</number>
               </property>
               <item row="1" column="0">
                <widget class="QToolButton" name="curve_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Curve - Interactive (Alt+C)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/spline.png</normaloff>:/toolicon/32x32/spline.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QToolButton" name="spline_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Spline - Interactive (Alt+S)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/splinePath.png</normaloff>:/toolicon/32x32/splinePath.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QToolButton" name="curveWithCPs_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Curve - Fixed (Alt+Shift+C)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/cubic_bezier.png</normaloff>:/toolicon/32x32/cubic_bezier.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QToolButton" name="splineWithCPs_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Spline - Fixed (Alt+Shift+S)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/cubic_bezier_path.png</normaloff>:/toolicon/32x32/cubic_bezier_path.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="6" column="1">
                <widget class="QToolButton" name="pointAlongSpline_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - On Spline (O, S)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/splinePath_cut_point.png</normaloff>:/toolicon/32x32/splinePath_cut_point.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="7" column="0">
                <widget class="QToolButton" name="pointOfIntersectionCurves_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Curves (I, C)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/intersection_curves.png</normaloff>:/toolicon/32x32/intersection_curves.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="7" column="1">
                <widget class="QToolButton" name="curveIntersectAxis_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Curve and Axis (C, X)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/curve_intersect_axis.png</normaloff>:/toolicon/32x32/curve_intersect_axis.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="6" column="0">
                <widget class="QToolButton" name="pointAlongCurve_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - On Curve (O, C)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/spline_cut_point.png</normaloff>:/toolicon/32x32/spline_cut_point.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="arcs_Page">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>128</width>
                <height>226</height>
               </rect>
              </property>
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="toolTip">
               <string>Tools for creating arcs.</string>
              </property>
              <attribute name="icon">
               <iconset resource="share/resources/toolicon.qrc">
                <normaloff>:/toolicon/32x32/arcs_toolbox.png</normaloff>:/toolicon/32x32/arcs_toolbox.png</iconset>
              </attribute>
              <attribute name="label">
               <string>Arc</string>
              </attribute>
              <attribute name="toolTip">
               <string>Arc</string>
              </attribute>
              <layout class="QFormLayout" name="formLayout_11">
               <property name="verticalSpacing">
                <number>4</number>
               </property>
               <property name="leftMargin">
                <number>8</number>
               </property>
               <property name="topMargin">
                <number>8</number>
               </property>
               <property name="rightMargin">
                <number>8</number>
               </property>
               <property name="bottomMargin">
                <number>8</number>
               </property>
               <item row="1" column="0">
                <widget class="QToolButton" name="arc_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Arc - Radius and Angles (Alt+A)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/arc.png</normaloff>:/toolicon/32x32/arc.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QToolButton" name="pointAlongArc_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - On Arc (O, A)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/arc_cut.png</normaloff>:/toolicon/32x32/arc_cut.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QToolButton" name="arcIntersectAxis_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Arc and Axis (A, X)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/arc_intersect_axis.png</normaloff>:/toolicon/32x32/arc_intersect_axis.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QToolButton" name="pointOfIntersectionArcs_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Arcs (I, A)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/point_of_intersection_arcs.png</normaloff>:/toolicon/32x32/point_of_intersection_arcs.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QToolButton" name="pointOfIntersectionCircles_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Circles (Shift+I, Shift+C)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/point_of_intersection_circles.png</normaloff>:/toolicon/32x32/point_of_intersection_circles.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QToolButton" name="pointFromCircleAndTangent_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Circle and Tangent (C, T)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/point_from_circle_and_tangent.png</normaloff>:/toolicon/32x32/point_from_circle_and_tangent.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="4" column="0">
                <widget class="QToolButton" name="pointFromArcAndTangent_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Point - Intersect Arc and Tangent (A, T)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/point_from_arc_and_tangent.png</normaloff>:/toolicon/32x32/point_from_arc_and_tangent.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QToolButton" name="arcWithLength_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Arc - Radius and Length (Alt+Shift+A)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/arc_with_length.png</normaloff>:/toolicon/32x32/arc_with_length.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="5" column="0">
                <widget class="QToolButton" name="ellipticalArc_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Arc - Elliptical (Alt+E)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/el_arc.png</normaloff>:/toolicon/32x32/el_arc.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="operations_Page">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>128</width>
                <height>180</height>
               </rect>
              </property>
              <property name="toolTip">
               <string>Tools for performing operations on objects</string>
              </property>
              <attribute name="icon">
               <iconset resource="share/resources/toolicon.qrc">
                <normaloff>:/toolicon/32x32/operations_toolbox.png</normaloff>:/toolicon/32x32/operations_toolbox.png</iconset>
              </attribute>
              <attribute name="label">
               <string>Operations</string>
              </attribute>
              <attribute name="toolTip">
               <string>Operations</string>
              </attribute>
              <layout class="QFormLayout" name="formLayout_13">
               <property name="verticalSpacing">
                <number>4</number>
               </property>
               <property name="leftMargin">
                <number>8</number>
               </property>
               <property name="topMargin">
                <number>8</number>
               </property>
               <property name="rightMargin">
                <number>8</number>
               </property>
               <property name="bottomMargin">
                <number>8</number>
               </property>
               <item row="0" column="0">
                <widget class="QToolButton" name="group_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Add Objects to Group (G)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/group.png</normaloff>:/toolicon/32x32/group.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QToolButton" name="rotation_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Rotate Selected Objects (R)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/rotation.png</normaloff>:/toolicon/32x32/rotation.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QToolButton" name="mirrorByLine_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Mirror Objects by Line (M, L)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/mirror_by_line.png</normaloff>:/toolicon/32x32/mirror_by_line.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QToolButton" name="mirrorByAxis_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Mirror Objects by Axis (M, A)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/mirror_by_axis.png</normaloff>:/toolicon/32x32/mirror_by_axis.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QToolButton" name="move_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Move Objects (Alt+M)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/move.png</normaloff>:/toolicon/32x32/move.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QToolButton" name="trueDarts_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>True Darts (T, D)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/true_darts.png</normaloff>:/toolicon/32x32/true_darts.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QToolButton" name="exportDraftBlocks_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Export Draft Blocks (E, D)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/export.png</normaloff>:/toolicon/32x32/export.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="piece_Page">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>128</width>
                <height>96</height>
               </rect>
              </property>
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="toolTip">
               <string>Tools for adding pattern pieces.</string>
              </property>
              <attribute name="icon">
               <iconset resource="share/resources/toolicon.qrc">
                <normaloff>:/toolicon/32x32/patternpiece_toolbox.png</normaloff>:/toolicon/32x32/patternpiece_toolbox.png</iconset>
              </attribute>
              <attribute name="label">
               <string>Piece</string>
              </attribute>
              <attribute name="toolTip">
               <string>Add Details</string>
              </attribute>
              <layout class="QFormLayout" name="formLayout_14">
               <property name="verticalSpacing">
                <number>4</number>
               </property>
               <property name="leftMargin">
                <number>8</number>
               </property>
               <property name="topMargin">
                <number>8</number>
               </property>
               <property name="rightMargin">
                <number>8</number>
               </property>
               <property name="bottomMargin">
                <number>8</number>
               </property>
               <item row="0" column="0">
                <widget class="QToolButton" name="addPatternPiece_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Add New Pattern Piece (N, P)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/new_piece.png</normaloff>:/toolicon/32x32/new_piece.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QToolButton" name="anchorPoint_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Add Anchor Point (A, P)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
                   <normaloff>:/icon/32x32/anchor_point.png</normaloff>:/icon/32x32/anchor_point.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QToolButton" name="insertNodes_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Insert Nodes (I, N)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/insert_nodes_icon.png</normaloff>:/toolicon/32x32/insert_nodes_icon.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QToolButton" name="internalPath_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Add Internal Path (I, P)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="share/resources/toolicon.qrc">
                   <normaloff>:/toolicon/32x32/path.png</normaloff>:/toolicon/32x32/path.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
             <widget class="QWidget" name="backgroundImage_Page">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>128</width>
                <height>56</height>
               </rect>
              </property>
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="toolTip">
               <string>Tools for inserting or modifying images.</string>
              </property>
              <attribute name="icon">
               <iconset resource="share/resources/toolicon.qrc">
                <normaloff>:/toolicon/32x32/image_menu_icon.png</normaloff>:/toolicon/32x32/image_menu_icon.png</iconset>
              </attribute>
              <attribute name="label">
               <string>Images</string>
              </attribute>
              <attribute name="toolTip">
               <string>Images</string>
              </attribute>
              <layout class="QFormLayout" name="formLayout">
               <item row="0" column="0">
                <widget class="QToolButton" name="importImage_ToolButton">
                 <property name="enabled">
                  <bool>false</bool>
                 </property>
                 <property name="toolTip">
                  <string>Import Image (Alt + I)</string>
                 </property>
                 <property name="text">
                  <string notr="true">...</string>
                 </property>
                 <property name="icon">
                  <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
                   <normaloff>:/icon/32x32/add_image.png</normaloff>:/icon/32x32/add_image.png</iconset>
                 </property>
                 <property name="iconSize">
                  <size>
                   <width>32</width>
                   <height>32</height>
                  </size>
                 </property>
                 <property name="checkable">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="pieceMode_Page">
          <widget class="QToolBox" name="piece_ToolBox">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>120</width>
             <height>111</height>
            </rect>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
             <horstretch>1</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="toolTip">
            <string/>
           </property>
           <property name="currentIndex">
            <number>0</number>
           </property>
           <property name="tabSpacing">
            <number>4</number>
           </property>
           <widget class="QWidget" name="details_Page">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>0</y>
              <width>98</width>
              <height>28</height>
             </rect>
            </property>
            <property name="toolTip">
             <string>Tools for adding details to pattern pieces</string>
            </property>
            <attribute name="icon">
             <iconset resource="share/resources/toolicon.qrc">
              <normaloff>:/toolicon/32x32/details_toolbox.png</normaloff>:/toolicon/32x32/details_toolbox.png</iconset>
            </attribute>
            <attribute name="label">
             <string>Details</string>
            </attribute>
            <attribute name="toolTip">
             <string>Add Details</string>
            </attribute>
            <widget class="QToolButton" name="unitePieces_ToolButton">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="geometry">
              <rect>
               <x>10</x>
               <y>10</y>
               <width>39</width>
               <height>38</height>
              </rect>
             </property>
             <property name="toolTip">
              <string>Unite 2 Pieces (U)</string>
             </property>
             <property name="text">
              <string notr="true">...</string>
             </property>
             <property name="icon">
              <iconset resource="share/resources/toolicon.qrc">
               <normaloff>:/toolicon/32x32/union.png</normaloff>:/toolicon/32x32/union.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>32</width>
               <height>32</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
            <widget class="QToolButton" name="exportPiecesAs_ToolButton">
             <property name="enabled">
              <bool>false</bool>
             </property>
             <property name="geometry">
              <rect>
               <x>60</x>
               <y>10</y>
               <width>39</width>
               <height>38</height>
              </rect>
             </property>
             <property name="toolTip">
              <string>Export Pieces (E, P)</string>
             </property>
             <property name="text">
              <string notr="true">...</string>
             </property>
             <property name="icon">
              <iconset resource="share/resources/toolicon.qrc">
               <normaloff>:/toolicon/32x32/export.png</normaloff>:/toolicon/32x32/export.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>32</width>
               <height>32</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
            </widget>
           </widget>
          </widget>
         </widget>
         <widget class="QWidget" name="layoutMode_Page">
          <widget class="QToolBox" name="layout_ToolBox">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>120</width>
             <height>111</height>
            </rect>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
             <horstretch>1</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>0</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="toolTip">
            <string/>
           </property>
           <property name="currentIndex">
            <number>0</number>
           </property>
           <property name="tabSpacing">
            <number>4</number>
           </property>
           <widget class="QWidget" name="layout_Page">
            <property name="geometry">
             <rect>
              <x>0</x>
              <y>0</y>
              <width>100</width>
              <height>54</height>
             </rect>
            </property>
            <attribute name="icon">
             <iconset resource="share/resources/toolicon.qrc">
              <normaloff>:/toolicon/32x32/layout_toolbox.png</normaloff>:/toolicon/32x32/layout_toolbox.png</iconset>
            </attribute>
            <attribute name="label">
             <string>Layout</string>
            </attribute>
            <attribute name="toolTip">
             <string>Layout</string>
            </attribute>
            <layout class="QFormLayout" name="formLayout_15">
             <property name="fieldGrowthPolicy">
              <enum>QFormLayout::AllNonFixedFieldsGrow</enum>
             </property>
             <property name="verticalSpacing">
              <number>4</number>
             </property>
             <property name="leftMargin">
              <number>8</number>
             </property>
             <property name="topMargin">
              <number>8</number>
             </property>
             <property name="rightMargin">
              <number>8</number>
             </property>
             <property name="bottomMargin">
              <number>8</number>
             </property>
             <item row="0" column="0">
              <widget class="QToolButton" name="layoutSettings_ToolButton">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="toolTip">
                <string>New Print Layout (N, L)</string>
               </property>
               <property name="text">
                <string>Settings</string>
               </property>
               <property name="icon">
                <iconset resource="share/resources/toolicon.qrc">
                 <normaloff>:/toolicon/32x32/layout_settings.png</normaloff>:/toolicon/32x32/layout_settings.png</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>32</width>
                 <height>32</height>
                </size>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QToolButton" name="exportLayout_ToolButton">
               <property name="enabled">
                <bool>false</bool>
               </property>
               <property name="toolTip">
                <string>Export Layout (E, L)</string>
               </property>
               <property name="text">
                <string notr="true">...</string>
               </property>
               <property name="icon">
                <iconset resource="share/resources/toolicon.qrc">
                 <normaloff>:/toolicon/32x32/export.png</normaloff>:/toolicon/32x32/export.png</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>32</width>
                 <height>32</height>
                </size>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </widget>
         </widget>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QToolBar" name="view_ToolBar">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="windowTitle">
    <string>View Toolbar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="toggleWireframe_Action"/>
   <addaction name="toggleControlPoints_Action"/>
   <addaction name="toggleAxisOrigin_Action"/>
   <addaction name="toggleSeamAllowances_Action"/>
   <addaction name="toggleGrainLines_Action"/>
   <addaction name="toggleLabels_Action"/>
  </widget>
  <action name="actionNew">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/32x32/actions/document-new.png</normaloff>:/icons/win.icon.theme/32x32/actions/document-new.png</iconset>
   </property>
   <property name="text">
    <string>New</string>
   </property>
   <property name="iconText">
    <string>&amp;New</string>
   </property>
   <property name="toolTip">
    <string>Create a new pattern</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+N</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionOpen">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/seamly2d_file.png</normaloff>:/icon/32x32/seamly2d_file.png</iconset>
   </property>
   <property name="text">
    <string>Open</string>
   </property>
   <property name="iconText">
    <string>&amp;Open</string>
   </property>
   <property name="toolTip">
    <string>Open file with pattern</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+O</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="save_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/32x32/actions/document-save.png</normaloff>:/icons/win.icon.theme/32x32/actions/document-save.png</iconset>
   </property>
   <property name="text">
    <string>Save</string>
   </property>
   <property name="iconText">
    <string>Save</string>
   </property>
   <property name="toolTip">
    <string>Save pattern</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+S</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="saveAs_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset theme="document-save-as">
     <normaloff>.</normaloff>.</iconset>
   </property>
   <property name="text">
    <string>Save As...</string>
   </property>
   <property name="toolTip">
    <string>Save not yet saved pattern</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Shift+S</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="showDraftMode">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/draw.png</normaloff>:/icon/32x32/draw.png</iconset>
   </property>
   <property name="text">
    <string>Draft</string>
   </property>
   <property name="iconText">
    <string>Draft</string>
   </property>
   <property name="toolTip">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Mode for working with draft blocks. These draft blocks are the base for going to the next stage &amp;quot;Piece mode&amp;quot;. Before you will be able to enable the &amp;quot;Piece mode&amp;quot; you need to create at least one pattern piece.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
   <property name="shortcut">
    <string notr="true">Shift+D</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="pieceMode_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/kontur.png</normaloff>:/icon/32x32/kontur.png</iconset>
   </property>
   <property name="text">
    <string>Piece</string>
   </property>
   <property name="toolTip">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Mode for working with pattern pieces. Before you will be able to enable the &amp;quot;Piece mode&amp;quot; you need to create at least one pattern piece on the stage &amp;quot;Draft mode&amp;quot;. Pattern pieces created on this stage will be used for creating a layout. &lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
   <property name="shortcut">
    <string notr="true">Shift+P</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="arrow_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/arrow_cursor.png</normaloff>:/icon/32x32/arrow_cursor.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Pointer tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="newDraft_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/new_draw.png</normaloff>:/icon/32x32/new_draw.png</iconset>
   </property>
   <property name="text">
    <string>New Draft Block</string>
   </property>
   <property name="toolTip">
    <string>Add new draft block (Ctrl+Shift+N)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Shift+N</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="renameDraft_Action">
   <property name="enabled">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/option_draw.png</normaloff>:/icon/32x32/option_draw.png</iconset>
   </property>
   <property name="text">
    <string>Rename Draft Block</string>
   </property>
   <property name="toolTip">
    <string>Change the name of the draft block</string>
   </property>
   <property name="shortcut">
    <string notr="true">F2</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="table_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/table.png</normaloff>:/icon/32x32/table.png</iconset>
   </property>
   <property name="text">
    <string>Variables table</string>
   </property>
   <property name="toolTip">
    <string>Contains information about custom and internal variables</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+T</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="history_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/history.png</normaloff>:/icon/32x32/history.png</iconset>
   </property>
   <property name="text">
    <string>History</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+H</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="layoutMode_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/layout.png</normaloff>:/icon/32x32/layout.png</iconset>
   </property>
   <property name="text">
    <string>Layout</string>
   </property>
   <property name="toolTip">
    <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Mode for creating a layout of pattern pieces. This mode is available if at least one pattern piece was created in &amp;quot;Piece mode&amp;quot;. The layout can be exported to your preferred file format and saved.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
   </property>
   <property name="shortcut">
    <string notr="true">Shift+L</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="group_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/group.png</normaloff>:/toolicon/32x32/group.png</iconset>
   </property>
   <property name="text">
    <string>Add Objects to Group</string>
   </property>
   <property name="toolTip">
    <string>Add Objects to Group (G)</string>
   </property>
   <property name="shortcut">
    <string notr="true">G</string>
   </property>
  </action>
  <action name="rotation_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/rotation.png</normaloff>:/toolicon/32x32/rotation.png</iconset>
   </property>
   <property name="text">
    <string>Rotation</string>
   </property>
   <property name="iconText">
    <string>Rotation</string>
   </property>
   <property name="toolTip">
    <string>Rotate Selected Objects (R)</string>
   </property>
   <property name="shortcut">
    <string notr="true">R</string>
   </property>
  </action>
  <action name="mirrorByLine_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/mirror_by_line.png</normaloff>:/toolicon/32x32/mirror_by_line.png</iconset>
   </property>
   <property name="text">
    <string>Mirror by Line</string>
   </property>
   <property name="toolTip">
    <string>Mirror Objects by Line (M, L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">M, L</string>
   </property>
  </action>
  <action name="mirrorByAxis_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/mirror_by_axis.png</normaloff>:/toolicon/32x32/mirror_by_axis.png</iconset>
   </property>
   <property name="text">
    <string>Mirror by Axis</string>
   </property>
   <property name="toolTip">
    <string>Mirror Objects by Axis (M, A)</string>
   </property>
   <property name="shortcut">
    <string notr="true">M, A</string>
   </property>
  </action>
  <action name="move_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/move.png</normaloff>:/toolicon/32x32/move.png</iconset>
   </property>
   <property name="text">
    <string>Move</string>
   </property>
   <property name="toolTip">
    <string>Move Objects (Alt+M)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Alt+M</string>
   </property>
  </action>
  <action name="trueDarts_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/true_darts.png</normaloff>:/toolicon/32x32/true_darts.png</iconset>
   </property>
   <property name="text">
    <string>True Darts</string>
   </property>
   <property name="toolTip">
    <string>True Darts (T, D)</string>
   </property>
   <property name="shortcut">
    <string notr="true">T, D</string>
   </property>
  </action>
  <action name="ellipticalArc_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/el_arc.png</normaloff>:/toolicon/32x32/el_arc.png</iconset>
   </property>
   <property name="text">
    <string>Elliptical</string>
   </property>
   <property name="iconText">
    <string>Arc -Elliptical</string>
   </property>
   <property name="toolTip">
    <string>Arc - Elliptical (Alt+E)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Alt+E</string>
   </property>
  </action>
  <action name="midpoint_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/midpoint.png</normaloff>:/toolicon/32x32/midpoint.png</iconset>
   </property>
   <property name="text">
    <string>Midpoint on Line</string>
   </property>
   <property name="toolTip">
    <string>Point - Midpoint on Line (Shift+O, Shift+L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Shift+O, Shift+L</string>
   </property>
  </action>
  <action name="pointAlongLine_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/along_line.png</normaloff>:/toolicon/32x32/along_line.png</iconset>
   </property>
   <property name="text">
    <string>On Line</string>
   </property>
   <property name="toolTip">
    <string>Point - On Line (O, L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">O, L</string>
   </property>
  </action>
  <action name="pointAtDistanceAngle_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/segment.png</normaloff>:/toolicon/32x32/segment.png</iconset>
   </property>
   <property name="text">
    <string>Length and Angle</string>
   </property>
   <property name="iconText">
    <string>Length and Angle</string>
   </property>
   <property name="toolTip">
    <string>Point - Length and Angle (L, A)</string>
   </property>
   <property name="shortcut">
    <string notr="true">L, A</string>
   </property>
  </action>
  <action name="pointAlongPerpendicular_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/normal.png</normaloff>:/toolicon/32x32/normal.png</iconset>
   </property>
   <property name="text">
    <string>On Perpendicular</string>
   </property>
   <property name="toolTip">
    <string>Point - On Perpendicular (O, P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">O, P</string>
   </property>
  </action>
  <action name="bisector_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/bisector.png</normaloff>:/toolicon/32x32/bisector.png</iconset>
   </property>
   <property name="text">
    <string>On Bisector</string>
   </property>
   <property name="toolTip">
    <string>Point - On Bisector (O, B)</string>
   </property>
   <property name="shortcut">
    <string notr="true">O, B</string>
   </property>
  </action>
  <action name="pointOnShoulder_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/shoulder.png</normaloff>:/toolicon/32x32/shoulder.png</iconset>
   </property>
   <property name="text">
    <string>Length to Line</string>
   </property>
   <property name="toolTip">
    <string>Point - Length to Line (P, S)</string>
   </property>
   <property name="shortcut">
    <string notr="true">P, S</string>
   </property>
  </action>
  <action name="pointOfContact_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/point_intersect_arc_line.png</normaloff>:/toolicon/32x32/point_intersect_arc_line.png</iconset>
   </property>
   <property name="text">
    <string>Intersect  Arc and Line</string>
   </property>
   <property name="toolTip">
    <string>Point - Intersect Arc and Line (A, L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">A, L</string>
   </property>
  </action>
  <action name="triangle_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/triangle.png</normaloff>:/toolicon/32x32/triangle.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Axis and Triangle</string>
   </property>
   <property name="toolTip">
    <string>Point - Intersect Axis and Triangle (X, T)</string>
   </property>
   <property name="shortcut">
    <string notr="true">X, T</string>
   </property>
  </action>
  <action name="pointIntersectXY_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/point_intersectxy_icon.png</normaloff>:/toolicon/32x32/point_intersectxy_icon.png</iconset>
   </property>
   <property name="text">
    <string>Intersect XY</string>
   </property>
   <property name="toolTip">
    <string>Point - Intersect XY (X, Y)</string>
   </property>
   <property name="shortcut">
    <string notr="true">X, Y</string>
   </property>
  </action>
  <action name="perpendicularPoint_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/height.png</normaloff>:/toolicon/32x32/height.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Line and Perpendicular</string>
   </property>
   <property name="toolTip">
    <string>Point - Intersect Line and Perpendicular (L, P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">L, P</string>
   </property>
  </action>
  <action name="pointIntersectAxis_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/line_intersect_axis.png</normaloff>:/toolicon/32x32/line_intersect_axis.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Line and Axis</string>
   </property>
   <property name="toolTip">
    <string>Point - Intersect Line and Axis (L, X)</string>
   </property>
   <property name="shortcut">
    <string notr="true">L, X</string>
   </property>
  </action>
  <action name="lineIntersect_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/intersect.png</normaloff>:/toolicon/32x32/intersect.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Lines</string>
   </property>
   <property name="toolTip">
    <string>Point - Intersect Lines (I, L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">I, L</string>
   </property>
  </action>
  <action name="curve_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/spline.png</normaloff>:/toolicon/32x32/spline.png</iconset>
   </property>
   <property name="text">
    <string>Curve - Interactive</string>
   </property>
   <property name="toolTip">
    <string>Curve - Interactive (Alt+C)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Alt+C</string>
   </property>
  </action>
  <action name="pointAlongCurve_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/spline_cut_point.png</normaloff>:/toolicon/32x32/spline_cut_point.png</iconset>
   </property>
   <property name="text">
    <string>Point on Curve</string>
   </property>
   <property name="toolTip">
    <string>Point on Curve (O, C)</string>
   </property>
   <property name="shortcut">
    <string notr="true">O, C</string>
   </property>
  </action>
  <action name="curveWithCPs_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/cubic_bezier.png</normaloff>:/toolicon/32x32/cubic_bezier.png</iconset>
   </property>
   <property name="text">
    <string>Curve - Fixed</string>
   </property>
   <property name="toolTip">
    <string>Curve - Fixed (Alt+Shift+C)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Alt+Shift+C</string>
   </property>
  </action>
  <action name="spline_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/splinePath.png</normaloff>:/toolicon/32x32/splinePath.png</iconset>
   </property>
   <property name="text">
    <string>Spline - Interactive</string>
   </property>
   <property name="toolTip">
    <string>Spline - Interactive (Alt+S)</string>
   </property>
   <property name="shortcut">
    <string>Alt+S</string>
   </property>
  </action>
  <action name="pointAlongSpline_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/splinePath_cut_point.png</normaloff>:/toolicon/32x32/splinePath_cut_point.png</iconset>
   </property>
   <property name="text">
    <string>Point on Spline</string>
   </property>
   <property name="toolTip">
    <string>Point on Spline (O, S)</string>
   </property>
   <property name="shortcut">
    <string notr="true">O, S</string>
   </property>
  </action>
  <action name="splineWithCPs_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/cubic_bezier_path.png</normaloff>:/toolicon/32x32/cubic_bezier_path.png</iconset>
   </property>
   <property name="text">
    <string>Spline - Fixed</string>
   </property>
   <property name="toolTip">
    <string>Spline - Fixed (Alt+Shift+S)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Alt+Shift+S</string>
   </property>
  </action>
  <action name="curveIntersectCurve_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/intersection_curves.png</normaloff>:/toolicon/32x32/intersection_curves.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Curves</string>
   </property>
   <property name="iconText">
    <string>Intersect Curves</string>
   </property>
   <property name="toolTip">
    <string>Point - Intersect Curves (I, C)</string>
   </property>
   <property name="shortcut">
    <string notr="true">I, C</string>
   </property>
  </action>
  <action name="splineIntersectAxis_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/curve_intersect_axis.png</normaloff>:/toolicon/32x32/curve_intersect_axis.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Curve and Axis</string>
   </property>
   <property name="iconText">
    <string>Intersect Curve and Axis</string>
   </property>
   <property name="toolTip">
    <string>Point - Intersect Curve and Axis (C, X)</string>
   </property>
   <property name="shortcut">
    <string notr="true">C, X</string>
   </property>
  </action>
  <action name="arcTool_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/arc.png</normaloff>:/toolicon/32x32/arc.png</iconset>
   </property>
   <property name="text">
    <string>Radius and Angles</string>
   </property>
   <property name="iconText">
    <string>Arc - Radius and Angles</string>
   </property>
   <property name="toolTip">
    <string>Arc - Radius and Angles (Alt+A)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Alt+A</string>
   </property>
  </action>
  <action name="pointAlongArc_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/arc_cut.png</normaloff>:/toolicon/32x32/arc_cut.png</iconset>
   </property>
   <property name="text">
    <string>Point on Arc</string>
   </property>
   <property name="toolTip">
    <string>Point on Arc (O, A)</string>
   </property>
   <property name="shortcut">
    <string notr="true">O, A</string>
   </property>
  </action>
  <action name="arcIntersectAxis_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/arc_intersect_axis.png</normaloff>:/toolicon/32x32/arc_intersect_axis.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Arc and Axis</string>
   </property>
   <property name="toolTip">
    <string>Intersect Arc and Axis (A, X)</string>
   </property>
   <property name="shortcut">
    <string notr="true">A, X</string>
   </property>
  </action>
  <action name="arcIntersectArc_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/point_of_intersection_arcs.png</normaloff>:/toolicon/32x32/point_of_intersection_arcs.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Arcs</string>
   </property>
   <property name="toolTip">
    <string>Intersect Arcs (I, A)</string>
   </property>
   <property name="shortcut">
    <string notr="true">I, A</string>
   </property>
  </action>
  <action name="circleIntersect_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/point_of_intersection_circles.png</normaloff>:/toolicon/32x32/point_of_intersection_circles.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Circles</string>
   </property>
   <property name="toolTip">
    <string>Intersect Circles (Shift+I, Shift+C)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Shift+I, Shift+C</string>
   </property>
  </action>
  <action name="circleTangent_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/point_from_circle_and_tangent.png</normaloff>:/toolicon/32x32/point_from_circle_and_tangent.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Circle and Tangent</string>
   </property>
   <property name="toolTip">
    <string>Intersect Circle and Tangent (C, T)</string>
   </property>
   <property name="shortcut">
    <string notr="true">C, T</string>
   </property>
  </action>
  <action name="arcTangent_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/point_from_arc_and_tangent.png</normaloff>:/toolicon/32x32/point_from_arc_and_tangent.png</iconset>
   </property>
   <property name="text">
    <string>Intersect Arc and Tangent</string>
   </property>
   <property name="toolTip">
    <string>Intersect Arc and Tangent (A, T)</string>
   </property>
   <property name="shortcut">
    <string notr="true">A, T</string>
   </property>
  </action>
  <action name="arcWithLength_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/arc_with_length.png</normaloff>:/toolicon/32x32/arc_with_length.png</iconset>
   </property>
   <property name="text">
    <string>Radius and Length</string>
   </property>
   <property name="iconText">
    <string>Arc - Radius and Length</string>
   </property>
   <property name="toolTip">
    <string>Arc - Radius and Length (Alt+Shift+A)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Alt+Shift+A</string>
   </property>
  </action>
  <action name="aboutQt_Action">
   <property name="text">
    <string>About &amp;Qt</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::AboutQtRole</enum>
   </property>
  </action>
  <action name="aboutSeamly2D_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/logos/seamly_logo_32.png</normaloff>:/icon/logos/seamly_logo_32.png</iconset>
   </property>
   <property name="text">
    <string>About Seamly2D</string>
   </property>
   <property name="toolTip">
    <string>About Seamly2D</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::AboutRole</enum>
   </property>
  </action>
  <action name="exit_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/32x32/actions/application-exit.png</normaloff>:/icons/win.icon.theme/32x32/actions/application-exit.png</iconset>
   </property>
   <property name="text">
    <string>E&amp;xit</string>
   </property>
   <property name="toolTip">
    <string>Exit the Application</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Q</string>
   </property>
   <property name="menuRole">
    <enum>QAction::QuitRole</enum>
   </property>
  </action>
  <action name="appPreferences_Action">
   <property name="text">
    <string>Application Preferences...</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+,</string>
   </property>
   <property name="menuRole">
    <enum>QAction::PreferencesRole</enum>
   </property>
  </action>
  <action name="patternPreferences_Action">
   <property name="text">
    <string>Pattern Preferences...</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Shift+,</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="zoomIn_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_in.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_in.png</iconset>
   </property>
   <property name="text">
    <string>Zoom In</string>
   </property>
   <property name="iconText">
    <string>In</string>
   </property>
   <property name="toolTip">
    <string>Zoom In (Ctrl++)</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="zoomOut_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_out.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_out.png</iconset>
   </property>
   <property name="text">
    <string>Zoom Out</string>
   </property>
   <property name="iconText">
    <string>Out</string>
   </property>
   <property name="toolTip">
    <string>Zoom Out (Ctrl+-)</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="actionZoomOriginal">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset theme="zoom-original">
     <normaloff>.</normaloff>.</iconset>
   </property>
   <property name="text">
    <string>Original zoom</string>
   </property>
   <property name="toolTip">
    <string>Original zoom</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="zoomToFit_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_fit.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_fit.png</iconset>
   </property>
   <property name="text">
    <string>Fit All</string>
   </property>
   <property name="iconText">
    <string>Fit</string>
   </property>
   <property name="toolTip">
    <string>Zoom to Fit All(Ctrl+9)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+9</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="reportBug_Action">
   <property name="text">
    <string>Report bug...</string>
   </property>
   <property name="toolTip">
    <string>Report bug</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="closePattern_Action">
   <property name="text">
    <string>Close</string>
   </property>
   <property name="iconText">
    <string>Close</string>
   </property>
   <property name="toolTip">
    <string>Close pattern</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+W</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="wiki_Action">
   <property name="icon">
    <iconset theme="help-contents">
     <normaloff>.</normaloff>.</iconset>
   </property>
   <property name="text">
    <string>Wiki</string>
   </property>
   <property name="toolTip">
    <string>Show online help</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="shortcuts_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/2d_key_icon.png</normaloff>:/icon/32x32/2d_key_icon.png</iconset>
   </property>
   <property name="text">
    <string>Shortcuts</string>
   </property>
   <property name="shortcut">
    <string notr="true">K</string>
   </property>
  </action>
  <action name="lastTool_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Last tool</string>
   </property>
   <property name="toolTip">
    <string>Activate last used tool (Ctrl+Shift+L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Shift+L</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="toggleControlPoints_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/curve_control_points.png</normaloff>:/icon/32x32/curve_control_points.png</iconset>
   </property>
   <property name="text">
    <string>Curve Control Points</string>
   </property>
   <property name="toolTip">
    <string>Toggle Control Points and Curve Direction (V, C)</string>
   </property>
   <property name="shortcut">
    <string notr="true">V, C</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="print_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset theme="document-print">
     <normaloff>.</normaloff>.</iconset>
   </property>
   <property name="text">
    <string>Print</string>
   </property>
   <property name="toolTip">
    <string>Print an original layout</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="printTiled_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset theme="document-print">
     <normaloff>.</normaloff>.</iconset>
   </property>
   <property name="text">
    <string>Print tiled PDF</string>
   </property>
   <property name="toolTip">
    <string>Split and print a layout into smaller pages (for regular printers)</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="printPreview_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset theme="document-print-preview">
     <normaloff>.</normaloff>.</iconset>
   </property>
   <property name="text">
    <string>Print preview</string>
   </property>
   <property name="toolTip">
    <string>Print preview original layout</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="printPreviewTiled_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset theme="document-print-preview">
     <normaloff>.</normaloff>.</iconset>
   </property>
   <property name="text">
    <string>Preview tiled PDF</string>
   </property>
   <property name="toolTip">
    <string>Print preview tiled layout</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="exportAs_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/export.png</normaloff>:/toolicon/32x32/export.png</iconset>
   </property>
   <property name="text">
    <string>Export As...</string>
   </property>
   <property name="toolTip">
    <string>Export Layout (E, L)</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="loadIndividual_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/individual_size_file.png</normaloff>:/icon/32x32/individual_size_file.png</iconset>
   </property>
   <property name="text">
    <string>Load Individual</string>
   </property>
   <property name="toolTip">
    <string>Load Individual measurements file</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="loadMultisize_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/multisize_size_file.png</normaloff>:/icon/32x32/multisize_size_file.png</iconset>
   </property>
   <property name="text">
    <string>Load Multisize</string>
   </property>
   <property name="iconText">
    <string>Load multisize</string>
   </property>
   <property name="toolTip">
    <string>Load multisize measurements file</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="openSeamlyMe_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/logos/seamlyme_logo_32.png</normaloff>:/icon/logos/seamlyme_logo_32.png</iconset>
   </property>
   <property name="text">
    <string>Open SeamlyMe</string>
   </property>
   <property name="toolTip">
    <string>Open SeamlyMe measurements app (Ctrl+M)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+M</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="editCurrent_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Edit Current</string>
   </property>
   <property name="toolTip">
    <string>Edit linked to the pattern measurements</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="syncMeasurements_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/syncM.png</normaloff>:/icon/32x32/syncM.png</iconset>
   </property>
   <property name="text">
    <string>Sync</string>
   </property>
   <property name="toolTip">
    <string>Synchronize linked to the pattern measurements after change</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="unloadMeasurements_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Unload Current</string>
   </property>
   <property name="toolTip">
    <string>Unload measurements if they were not used in a pattern file</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="forum_Action">
   <property name="text">
    <string>Forum</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="exportVariablesToCSV_Action">
   <property name="text">
    <string>Export Variables to CSV</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+E</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="zoomToSelected_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_selection.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_selection.png</iconset>
   </property>
   <property name="text">
    <string>Selected</string>
   </property>
   <property name="iconText">
    <string>Selected</string>
   </property>
   <property name="toolTip">
    <string>Zoom to Selected (Ctrl+Right)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Right</string>
   </property>
   <property name="menuRole">
    <enum>QAction::NoRole</enum>
   </property>
  </action>
  <action name="labelTemplateEditor_Action">
   <property name="text">
    <string>Label Template Editor...</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="zoomToPrevious_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_previous.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_previous.png</iconset>
   </property>
   <property name="text">
    <string>Previous</string>
   </property>
   <property name="toolTip">
    <string>Zoom to Previous (Ctrl+Left)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Left</string>
   </property>
  </action>
  <action name="zoomToArea_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_area.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_area.png</iconset>
   </property>
   <property name="text">
    <string>Area</string>
   </property>
   <property name="toolTip">
    <string>Zoom to selected Area (Ctrl+A)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+A</string>
   </property>
  </action>
  <action name="zoomPan_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_pan.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_pan.png</iconset>
   </property>
   <property name="text">
    <string>Pan</string>
   </property>
   <property name="toolTip">
    <string>Pan Work Area (Z, P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Z, P</string>
   </property>
  </action>
  <action name="zoom100Percent_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_100_percent.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_100_percent.png</iconset>
   </property>
   <property name="text">
    <string>Zoom 100%</string>
   </property>
   <property name="iconText">
    <string notr="true">100%</string>
   </property>
   <property name="toolTip">
    <string>Zoom to 100 percent (Ctrl+0)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+0</string>
   </property>
  </action>
  <action name="points_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/points_toolbox.png</normaloff>:/toolicon/32x32/points_toolbox.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Point Tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="lines_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/lines_toolbox.png</normaloff>:/toolicon/32x32/lines_toolbox.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Line Tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="lineTool_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/line.png</normaloff>:/toolicon/32x32/line.png</iconset>
   </property>
   <property name="text">
    <string>Line</string>
   </property>
   <property name="toolTip">
    <string>Line between 2 Points (Alt+L)</string>
   </property>
   <property name="shortcut">
    <string>Alt+L</string>
   </property>
  </action>
  <action name="curves_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/spline_toolboxs.png</normaloff>:/toolicon/32x32/spline_toolboxs.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Curve Tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="arcs_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/arcs_toolbox.png</normaloff>:/toolicon/32x32/arcs_toolbox.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Arc Tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="modifications_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/operations_toolbox.png</normaloff>:/toolicon/32x32/operations_toolbox.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Operations Tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="layout_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/layout_toolbox.png</normaloff>:/toolicon/32x32/layout_toolbox.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Layout Tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="pieces_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/patternpiece_toolbox.png</normaloff>:/toolicon/32x32/patternpiece_toolbox.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Piece tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="addPiece_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/<EMAIL></normaloff>:/toolicon/32x32/<EMAIL></iconset>
   </property>
   <property name="text">
    <string>New Pattern Piece</string>
   </property>
   <property name="toolTip">
    <string>Add New Pattern Piece (N, P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">N, P</string>
   </property>
  </action>
  <action name="newPrintLayout_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/layout_settings.png</normaloff>:/toolicon/32x32/layout_settings.png</iconset>
   </property>
   <property name="text">
    <string>New Print Layout</string>
   </property>
   <property name="toolTip">
    <string>Create New Print Layout (N, L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">N, L</string>
   </property>
  </action>
  <action name="exportLayout_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/export.png</normaloff>:/toolicon/32x32/export.png</iconset>
   </property>
   <property name="text">
    <string>Export Layout</string>
   </property>
   <property name="toolTip">
    <string>Export Layout (E, L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">E, L</string>
   </property>
  </action>
  <action name="anchorPoint_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/anchor_point.png</normaloff>:/icon/32x32/anchor_point.png</iconset>
   </property>
   <property name="text">
    <string>Anchor Point</string>
   </property>
   <property name="toolTip">
    <string>Add Anchor Point (A, P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">A, P</string>
   </property>
  </action>
  <action name="internalPath_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/path.png</normaloff>:/toolicon/32x32/path.png</iconset>
   </property>
   <property name="text">
    <string>Internal Path</string>
   </property>
   <property name="toolTip">
    <string>Add Internal Path (I, P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">I, P</string>
   </property>
  </action>
  <action name="insertNodes_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/insert_nodes_icon.png</normaloff>:/toolicon/32x32/insert_nodes_icon.png</iconset>
   </property>
   <property name="text">
    <string>Insert Nodes</string>
   </property>
   <property name="toolTip">
    <string>Insert Nodes (I, N)</string>
   </property>
   <property name="shortcut">
    <string notr="true">I, N</string>
   </property>
  </action>
  <action name="union_Action">
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/union.png</normaloff>:/toolicon/32x32/union.png</iconset>
   </property>
   <property name="text">
    <string>Unite Pieces</string>
   </property>
   <property name="toolTip">
    <string>Unite 2 Pieces (U)</string>
   </property>
   <property name="shortcut">
    <string notr="true">U</string>
   </property>
  </action>
  <action name="exportPieces_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/export.png</normaloff>:/toolicon/32x32/export.png</iconset>
   </property>
   <property name="text">
    <string>Export Pieces</string>
   </property>
   <property name="toolTip">
    <string>Export Pieces (E, P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">E, P</string>
   </property>
  </action>
  <action name="details_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/details_toolbox.png</normaloff>:/toolicon/32x32/details_toolbox.png</iconset>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="toolTip">
    <string>Detail tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="showPointNames_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/16x16/<EMAIL></normaloff>:/icon/16x16/<EMAIL></iconset>
   </property>
   <property name="text">
    <string>Point Name Text</string>
   </property>
   <property name="toolTip">
    <string>Toggle Point Name Text (V, P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">V, P</string>
   </property>
  </action>
  <action name="increaseSize_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/increase_size.png</normaloff>:/icon/32x32/increase_size.png</iconset>
   </property>
   <property name="text">
    <string>Increase Text Size</string>
   </property>
   <property name="toolTip">
    <string>Increase Text Size (Ctrl+])</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+]</string>
   </property>
  </action>
  <action name="decreaseSize_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/decrease_size.png</normaloff>:/icon/32x32/decrease_size.png</iconset>
   </property>
   <property name="text">
    <string>Decrease Text Size</string>
   </property>
   <property name="toolTip">
    <string>Decrease Text Size (Ctrl+[)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+[</string>
   </property>
  </action>
  <action name="useToolColor_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/pen_color.png</normaloff>:/icon/32x32/pen_color.png</iconset>
   </property>
   <property name="text">
    <string>Use Tool Color</string>
   </property>
   <property name="toolTip">
    <string>Use Tool Color (T)</string>
   </property>
   <property name="shortcut">
    <string notr="true">V, T</string>
   </property>
  </action>
  <action name="toggleAxisOrigin_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/origin.png</normaloff>:/icon/32x32/origin.png</iconset>
   </property>
   <property name="text">
    <string>Axis Origin </string>
   </property>
   <property name="toolTip">
    <string>Toggle Axis Origin (V, A)</string>
   </property>
   <property name="shortcut">
    <string notr="true">V, A</string>
   </property>
  </action>
  <action name="toggleWireframe_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/wireframe.png</normaloff>:/icon/32x32/wireframe.png</iconset>
   </property>
   <property name="text">
    <string>Wireframe Mode</string>
   </property>
   <property name="toolTip">
    <string>Toggle Wireframe Mode (V, W)</string>
   </property>
   <property name="shortcut">
    <string notr="true">V, W</string>
   </property>
  </action>
  <action name="toggleGrainLines_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/grainline.png</normaloff>:/icon/32x32/grainline.png</iconset>
   </property>
   <property name="text">
    <string>Grainlines</string>
   </property>
   <property name="toolTip">
    <string>Toggle Grainlines (V, G)</string>
   </property>
   <property name="shortcut">
    <string notr="true">V, G</string>
   </property>
  </action>
  <action name="toggleLabels_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/labels.png</normaloff>:/icon/32x32/labels.png</iconset>
   </property>
   <property name="text">
    <string>Labels</string>
   </property>
   <property name="toolTip">
    <string>Toggle Labels (V, L)</string>
   </property>
   <property name="shortcut">
    <string notr="true">V, L</string>
   </property>
  </action>
  <action name="calculator_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/calculator.png</normaloff>:/icon/32x32/calculator.png</iconset>
   </property>
   <property name="text">
    <string>Calculator</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Shift+C</string>
   </property>
  </action>
  <action name="decimalChart_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/table.png</normaloff>:/icon/32x32/table.png</iconset>
   </property>
   <property name="text">
    <string>Decimal Chart</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Shift+D</string>
   </property>
  </action>
  <action name="exportDraftBlocks_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/export.png</normaloff>:/toolicon/32x32/export.png</iconset>
   </property>
   <property name="text">
    <string>Export Draft Blocks</string>
   </property>
   <property name="toolTip">
    <string>Export Draft Blocks (E, D)</string>
   </property>
   <property name="shortcut">
    <string notr="true">E, D</string>
   </property>
  </action>
  <action name="actionExport">
   <property name="text">
    <string>Export</string>
   </property>
  </action>
  <action name="images_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="share/resources/toolicon.qrc">
     <normaloff>:/toolicon/32x32/image_menu_icon.png</normaloff>:/toolicon/32x32/image_menu_icon.png</iconset>
   </property>
   <property name="text">
    <string>Images tools</string>
   </property>
   <property name="shortcut">
    <string notr="true"/>
   </property>
  </action>
  <action name="deleteImage_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/trashcan.png</normaloff>:/icon/32x32/trashcan.png</iconset>
   </property>
   <property name="text">
    <string>Delete</string>
   </property>
  </action>
  <action name="alignImage_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/alignment.png</normaloff>:/icon/32x32/alignment.png</iconset>
   </property>
   <property name="text">
    <string>Align</string>
   </property>
  </action>
  <action name="lockImage_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/lock_on.png</normaloff>:/icon/32x32/lock_on.png</iconset>
   </property>
   <property name="text">
    <string>Lock</string>
   </property>
  </action>
  <action name="toggleSeamAllowances_Action">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="checked">
    <bool>true</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/<EMAIL></normaloff>:/icon/32x32/<EMAIL></iconset>
   </property>
   <property name="text">
    <string>Seam Allowance</string>
   </property>
   <property name="shortcut">
    <string notr="true">V, S</string>
   </property>
  </action>
  <action name="documentInfo_Action">
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/16x16/show_info16_icon.png</normaloff>:/icon/16x16/show_info16_icon.png</iconset>
   </property>
   <property name="text">
    <string>Document Info...</string>
   </property>
   <property name="iconText">
    <string>Document Info</string>
   </property>
   <property name="toolTip">
    <string>Display document Info</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+I</string>
   </property>
  </action>
  <action name="zoomToPoint_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/theme.qrc">
     <normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_point.png</normaloff>:/icons/win.icon.theme/24x24/actions/zoom_to_point.png</iconset>
   </property>
   <property name="text">
    <string>Point</string>
   </property>
   <property name="toolTip">
    <string>Zoom to point (Ctrl + Alt + P)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+Alt+P</string>
   </property>
  </action>
  <action name="importImage_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="icon">
    <iconset resource="../../libs/vmisc/share/resources/icon.qrc">
     <normaloff>:/icon/32x32/add_image.png</normaloff>:/icon/32x32/add_image.png</iconset>
   </property>
   <property name="text">
    <string>Import Image</string>
   </property>
   <property name="toolTip">
    <string>Import Image (Alt + I)</string>
   </property>
   <property name="shortcut">
    <string notr="true">Alt+I</string>
   </property>
  </action>
  <action name="previousDraftBlock_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Previous Draft Block</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+PgUp</string>
   </property>
  </action>
  <action name="nextDraftBlock_Action">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Next Draft Block</string>
   </property>
   <property name="shortcut">
    <string notr="true">Ctrl+PgDown</string>
   </property>
  </action>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>VMainGraphicsView</class>
   <extends>QGraphicsView</extends>
   <header>vmaingraphicsview.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="share/resources/toolicon.qrc"/>
  <include location="../../libs/vmisc/share/resources/theme.qrc"/>
  <include location="../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections/>
</ui>
