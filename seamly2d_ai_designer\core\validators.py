"""
数据验证模块
"""

from typing import Dict, Any, List

def validate_measurements(measurements: Dict[str, float]) -> bool:
    """验证测量数据有效性
    
    Args:
        measurements: 测量数据字典
        
    Returns:
        bool: 数据是否有效
        
    Raises:
        ValueError: 当数据无效时抛出
    """
    required_fields = ["chest_width", "shoulder_width", "length"]
    
    # 检查必填字段
    for field in required_fields:
        if field not in measurements:
            raise ValueError(f"缺少必填测量数据: {field}")
    
    # 检查数据类型和值
    for name, value in measurements.items():
        if not isinstance(value, (int, float)):
            raise ValueError(f"测量数据 {name} 必须为数字，当前类型: {type(value)}")
        
        if value <= 0:
            raise ValueError(f"测量数据 {name} 必须为正数，当前值: {value}")
    
    return True

def validate_point_coordinates(x: str, y: str) -> bool:
    """验证点坐标公式
    
    Args:
        x: X坐标公式
        y: Y坐标公式
        
    Returns:
        bool: 坐标是否有效
    """
    # 基础验证 - 检查是否为空
    if not x or not y:
        raise ValueError("坐标公式不能为空")
    
    # 这里可以添加更复杂的公式验证逻辑
    return True 