#!/usr/bin/env python3
"""
完整T恤版型演示脚本
展示包含前片、后片和袖子的完整T恤版型生成
"""

import os
from seamly2d_ai_designer import TShirt, PatternConfig

def main():
    """完整T恤版型演示"""
    print("🎨 完整T恤版型生成演示")
    print("=" * 50)
    
    # 创建配置
    config = PatternConfig()
    print(f"✅ 配置加载完成，单位: {config.unit}")
    
    # 定义测量数据 - 更接近真实T恤
    measurements = {
        "chest_width": 47.0,    # 胸围/2 (cm)
        "shoulder_width": 12.4, # 肩宽 (cm)  
        "length": 61.0,         # 衣长 (cm)
        "neck_width": 16.4      # 领宽 (cm)
    }
    
    print("\n📏 测量数据:")
    for name, value in measurements.items():
        print(f"   {name}: {value} cm")
    
    # 创建完整T恤生成器
    print("\n🏭 创建完整T恤版型生成器...")
    tshirt = TShirt(config)
    
    # 设置参数
    ease = 2.5              # 放松量
    neck_depth_front = 7.0  # 前领深
    neck_depth_back = 2.0   # 后领深
    sleeve_length = 18.0    # 袖长
    armhole_depth = 20.0    # 袖窿深
    
    print(f"⚙️ 设置参数:")
    print(f"   放松量: {ease} cm")
    print(f"   前领深: {neck_depth_front} cm")
    print(f"   后领深: {neck_depth_back} cm")
    print(f"   袖长: {sleeve_length} cm")
    print(f"   袖窿深: {armhole_depth} cm")
    
    # 生成完整版型
    print("\n🚀 开始生成完整T恤版型...")
    print("   包含: 前片 + 后片 + 袖子")
    
    try:
        output_file = "output/complete_tshirt.sm2d"
        
        # 创建输出目录
        os.makedirs("output", exist_ok=True)
        
        # 链式调用生成完整版型
        pattern_file = (tshirt
                       .set_measurements(measurements)
                       .set_ease(ease)
                       .set_neck_depth(neck_depth_front, neck_depth_back)
                       .set_sleeve_length(sleeve_length)
                       .set_armhole_depth(armhole_depth)
                       .create_pattern(output_file, preview=True))
        
        print(f"✅ 完整T恤版型生成成功!")
        print(f"📁 版型文件: {pattern_file}")
        print(f"🖼️ 预览图: {output_file.replace('.sm2d', '_preview.png')}")
        
        # 验证文件
        if os.path.exists(pattern_file):
            file_size = os.path.getsize(pattern_file)
            print(f"📊 文件大小: {file_size} bytes")
        
        print(f"\n🎉 演示完成!")
        print(f"💡 提示: 现在你有了一个包含前片、后片和袖子的完整T恤版型!")
        print(f"📋 版型包含:")
        print(f"   ✅ 前片 - 带有前领口和袖窿")
        print(f"   ✅ 后片 - 带有后领口和袖窿")  
        print(f"   ✅ 袖子 - 带有袖山和袖口")
        print(f"🔧 可以在Seamly2D软件中打开 {pattern_file} 查看和编辑")
        
    except Exception as e:
        print(f"❌ 生成失败: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main()) 