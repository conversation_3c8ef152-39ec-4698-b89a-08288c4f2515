/******************************************************************************
**  libDXFrw - Library to read/write DXF files (ascii & binary)              **
**                                                                           **
**  Copyright (C) 2011-2015 <PERSON>, <EMAIL>               **
**                                                                           **
**  This library is free software, licensed under the terms of the GNU       **
**  General Public License as published by the Free Software Foundation,     **
**  either version 2 of the License, or (at your option) any later version.  **
**  You should have received a copy of the GNU General Public License        **
**  along with this program.  If not, see <http://www.gnu.org/licenses/>.    **
******************************************************************************/

#ifndef DRW_CLASSES_H
#define DRW_CLASSES_H


#include "drw_base.h"

class dxfReader;
class dxfWriter;

//! Class to handle classes entries
/*!
*  Class to handle classes table entries
*  TODO: verify the dxf read/write part
*  <AUTHOR>
*/
class DRW_Class {
public:
    DRW_Class()
        : recName(),
          className(),
          appName(),
          proxyFlag(),
          instanceCount(),
          wasaProxyFlag(),
          entityFlag()
    {}

    ~DRW_Class() = default;

    void parseCode(int code, dxfReader *reader);
    void write(dxfWriter *writer, DRW::Version ver);

public:
    UTF8STRING recName;      /*!< record name, code 1 */
    UTF8STRING className;    /*!< C++ class name, code 2 */
    UTF8STRING appName;      /*!< app name, code 3 */
    int proxyFlag;           /*!< Proxy capabilities flag, code 90 */
    int instanceCount;       /*!< number of instances for a custom class, code 91*/
    int wasaProxyFlag;       /*!< proxy flag (app loaded on save), code 280 */
    int entityFlag;          /*!< entity flag, code 281 (0 object, 1 entity)*/
};

#endif

// EOF
