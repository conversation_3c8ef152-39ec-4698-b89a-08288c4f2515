<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ExportLayoutDialog</class>
 <widget class="QDialog" name="ExportLayoutDialog">
  <property name="windowModality">
   <enum>Qt::ApplicationModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>750</width>
    <height>400</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>750</width>
    <height>360</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>900</width>
    <height>400</height>
   </size>
  </property>
  <property name="font">
   <font>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Export Layout</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../share/resources/toolicon.qrc">
    <normaloff>:/toolicon/32x32/export.png</normaloff>:/toolicon/32x32/export.png</iconset>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QFormLayout" name="formLayout">
   <item row="0" column="0">
    <widget class="QCheckBox" name="binaryDXF_CheckBox">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="text">
      <string>Binary form</string>
     </property>
    </widget>
   </item>
   <item row="1" column="0">
    <widget class="QCheckBox" name="textAsPaths_Checkbox">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string>Text as paths</string>
     </property>
    </widget>
   </item>
   <item row="3" column="0" colspan="2">
    <widget class="QFrame" name="frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>140</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_6">
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <layout class="QHBoxLayout" name="path_HorizontalLayout">
          <property name="leftMargin">
           <number>10</number>
          </property>
          <property name="rightMargin">
           <number>10</number>
          </property>
          <item>
           <widget class="QLabel" name="path_Label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>120</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>Path:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="path_LineEdit">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>420</width>
              <height>0</height>
             </size>
            </property>
            <property name="toolTip">
             <string>Destination folder</string>
            </property>
            <property name="placeholderText">
             <string>Path to destination folder</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QPushButton" name="browse_PushButton">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="toolTip">
             <string>Select path to destination folder</string>
            </property>
            <property name="text">
             <string>Browse...</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QGroupBox" name="file_GroupBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>350</width>
            <height>110</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>110</height>
           </size>
          </property>
          <property name="title">
           <string/>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <layout class="QFormLayout" name="file_FormLayout">
             <property name="verticalSpacing">
              <number>9</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="format_Label">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>File format:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="ExportFormatCombobox" name="format_ComboBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="filename_Label">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>File name:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QLineEdit" name="filename_LineEdit">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="toolTip">
                <string>File base name</string>
               </property>
               <property name="placeholderText">
                <string>File base name</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="quality_Label">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Quality (0-100):</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <layout class="QHBoxLayout" name="quality_HorizontalLayout">
               <property name="spacing">
                <number>9</number>
               </property>
               <item>
                <widget class="QLabel" name="minus_Label">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>10</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">-</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                 </property>
                 <property name="indent">
                  <number>0</number>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSlider" name="quality_Slider">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>22</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QSlider::groove:horizontal {
border: 1px solid #bbb;
background: white;
height: 10px;
border-radius: 4px;
}

QSlider::sub-page:horizontal {
background: qlineargradient(x1: 0, y1: 0,    x2: 0, y2: 1,
stop: 0 #66e, stop: 1 #bbf);
background: qlineargradient(x1: 0, y1: 0.2, x2: 1, y2: 1,
stop: 0 #bbf, stop: 1 #55f);
border: 1px solid #777;
height: 10px;
border-radius: 4px;
}

QSlider::add-page:horizontal {
background: #fff;
border: 1px solid #777;
height: 10px;
border-radius: 4px;
}

QSlider::handle:horizontal {
background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
stop:0 #eee, stop:1 #ccc);
border: 1px solid #777;
width: 13px;
margin-top: -2px;
margin-bottom: -2px;
border-radius: 4px;
}

QSlider::handle:horizontal:hover {
background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
stop:0 #fff, stop:1 #ddd);
border: 1px solid #444;
border-radius: 4px;
}

QSlider::sub-page:horizontal:disabled {
background: #bbb;
border-color: #999;
}

QSlider::add-page:horizontal:disabled {
background: #eee;
border-color: #999;
}

QSlider::handle:horizontal:disabled {
background: #eee;
border: 1px solid #aaa;
border-radius: 4px;
}</string>
                 </property>
                 <property name="maximum">
                  <number>100</number>
                 </property>
                 <property name="value">
                  <number>75</number>
                 </property>
                 <property name="sliderPosition">
                  <number>75</number>
                 </property>
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="plus_Label">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>10</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>9</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">+</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="percent_SpinBox">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="buttonSymbols">
                  <enum>QAbstractSpinBox::NoButtons</enum>
                 </property>
                 <property name="maximum">
                  <number>100</number>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="export_GroupBox">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>250</width>
            <height>110</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>110</height>
           </size>
          </property>
          <property name="title">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <property name="topMargin">
              <number>5</number>
             </property>
             <item>
              <widget class="QLabel" name="exportFiles_Label">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="text">
                <string>Export files:</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignRight|Qt::AlignTop|Qt::AlignTrailing</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QListWidget" name="exportFiles_ListWidget">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>180</width>
                 <height>80</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>80</height>
                </size>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item row="5" column="1">
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Cancel|QDialogButtonBox::Save</set>
     </property>
    </widget>
   </item>
   <item row="2" column="0" colspan="2">
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="leftMargin">
      <number>10</number>
     </property>
     <property name="rightMargin">
      <number>10</number>
     </property>
     <item>
      <widget class="QGroupBox" name="margins_GroupBox">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>300</width>
         <height>88</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="title">
        <string>Margins</string>
       </property>
       <property name="checkable">
        <bool>false</bool>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <layout class="QHBoxLayout" name="margins_HorizontalLayout">
          <item>
           <layout class="QFormLayout" name="margins_FormLayout">
            <item row="1" column="0">
             <widget class="QLabel" name="rightField_Label">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Right:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QDoubleSpinBox" name="rightField_DoubleSpinBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>70</width>
                <height>0</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QDoubleSpinBox" name="leftField_DoubleSpinBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>70</width>
                <height>0</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="QLabel" name="leftField_Label">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Left:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QFormLayout" name="formLayout_3">
            <item row="0" column="0">
             <widget class="QLabel" name="topField_Label">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Top:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QDoubleSpinBox" name="topField_DoubleSpinBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>70</width>
                <height>0</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="bottomField_Label">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Bottom:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QDoubleSpinBox" name="bottomField_DoubleSpinBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>70</width>
                <height>0</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="paperFormat_GroupBox">
       <property name="enabled">
        <bool>false</bool>
       </property>
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>290</width>
         <height>88</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="title">
        <string>Paper format</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QFormLayout" name="paperFormat_FormLayout">
          <item row="0" column="0">
           <widget class="QLabel" name="templates_Label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>Templates: </string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="PageFormatCombobox" name="templates_ComboBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>0</height>
             </size>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="orientation_Label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>0</height>
             </size>
            </property>
            <property name="text">
             <string>Orientation: </string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <layout class="QHBoxLayout" name="orientation_HorizontalLayout">
            <item>
             <widget class="QToolButton" name="portrait_ToolButton">
              <property name="text">
               <string notr="true">...</string>
              </property>
              <property name="icon">
               <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                <normaloff>:/icon/16x16/portrait.png</normaloff>:/icon/16x16/portrait.png</iconset>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QToolButton" name="landscape_ToolButton">
              <property name="text">
               <string notr="true">...</string>
              </property>
              <property name="icon">
               <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                <normaloff>:/icon/16x16/landscape.png</normaloff>:/icon/16x16/landscape.png</iconset>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="autoExclusive">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item row="4" column="1">
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ExportFormatCombobox</class>
   <extends>QComboBox</extends>
   <header>export_format_combobox.h</header>
  </customwidget>
  <customwidget>
   <class>PageFormatCombobox</class>
   <extends>QComboBox</extends>
   <header>page_format_combobox.h</header>
  </customwidget>
 </customwidgets>
 <tabstops>
  <tabstop>path_LineEdit</tabstop>
  <tabstop>browse_PushButton</tabstop>
  <tabstop>binaryDXF_CheckBox</tabstop>
  <tabstop>textAsPaths_Checkbox</tabstop>
  <tabstop>leftField_DoubleSpinBox</tabstop>
  <tabstop>topField_DoubleSpinBox</tabstop>
  <tabstop>rightField_DoubleSpinBox</tabstop>
  <tabstop>bottomField_DoubleSpinBox</tabstop>
  <tabstop>portrait_ToolButton</tabstop>
  <tabstop>landscape_ToolButton</tabstop>
 </tabstops>
 <resources>
  <include location="../share/resources/toolicon.qrc"/>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>ExportLayoutDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>ExportLayoutDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
