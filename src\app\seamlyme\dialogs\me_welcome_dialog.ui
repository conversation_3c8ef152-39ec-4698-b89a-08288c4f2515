<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SeamlyMeWelcomeDialog</class>
 <widget class="QDialog" name="SeamlyMeWelcomeDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>650</width>
    <height>450</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>650</width>
    <height>450</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Welcome</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/logos/seamlyme_logo_32.png</normaloff>:/icon/logos/seamlyme_logo_32.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_4">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3">
     <item>
      <widget class="QLabel" name="seamlyme_banner_Label">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Fixed" vsizetype="Minimum">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>70</width>
         <height>400</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap resource="../share/resources/seamlymeicon.qrc">:/seamlymeicon/seamlyme_vertical.png</pixmap>
       </property>
       <property name="scaledContents">
        <bool>true</bool>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="bodyscan_GroupBox">
       <property name="font">
        <font>
         <pointsize>14</pointsize>
         <bold>true</bold>
        </font>
       </property>
       <property name="title">
        <string>Welcome to SeamlyME</string>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <widget class="QLabel" name="label_6">
            <property name="font">
             <font>
              <pointsize>14</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>3D Look users</string>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="QLabel" name="bodyscan_Label">
              <property name="maximumSize">
               <size>
                <width>96</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="pixmap">
               <pixmap resource="../../../libs/vmisc/share/resources/icon.qrc">:/icon/body_scan.png</pixmap>
              </property>
              <property name="scaledContents">
               <bool>false</bool>
              </property>
              <property name="alignment">
               <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout">
              <item>
               <widget class="QLabel" name="label_3">
                <property name="font">
                 <font>
                  <pointsize>10</pointsize>
                  <bold>false</bold>
                 </font>
                </property>
                <property name="text">
                 <string>To utilize a 3D Look body scan the file needs to be converted to SeamlyME format. </string>
                </property>
                <property name="wordWrap">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_4">
                <property name="font">
                 <font>
                  <pointsize>10</pointsize>
                  <bold>false</bold>
                 </font>
                </property>
                <property name="text">
                 <string>Attach your 3DLook file to an email and <NAME_EMAIL>.</string>
                </property>
                <property name="wordWrap">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_5">
                <property name="font">
                 <font>
                  <pointsize>10</pointsize>
                  <bold>false</bold>
                 </font>
                </property>
                <property name="text">
                 <string>You will recieve an email with the converted file, which you can then load in SeamlyME as usual.</string>
                </property>
                <property name="wordWrap">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QFormLayout" name="formLayout">
          <item row="0" column="1">
           <widget class="QLabel" name="label_2">
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Please choose your preferred units, decimal separator, and language. (You can change these later.)</string>
            </property>
            <property name="wordWrap">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="units_Label">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Units:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QComboBox" name="units_ComboBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="toolTip">
             <string>Sets the default units for a new measurement file.</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="seperastor_Label">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>Decimal separator:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QCheckBox" name="separator_CheckBox">
            <property name="minimumSize">
             <size>
              <width>300</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="toolTip">
             <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;Selects what decimal separator char to use.  When checked the separator for the user's locale is used.  When unchecked the period is used.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
            </property>
            <property name="text">
             <string notr="true">&lt; User locale &gt;</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="language_Label">
            <property name="minimumSize">
             <size>
              <width>150</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="text">
             <string>GUI language:</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QComboBox" name="language_ComboBox">
            <property name="minimumSize">
             <size>
              <width>300</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>10</pointsize>
              <bold>false</bold>
             </font>
            </property>
            <property name="toolTip">
             <string>Sets the language used for SeamlyMe.</string>
            </property>
           </widget>
          </item>
          <item row="4" column="1">
           <layout class="QHBoxLayout" name="horizontalLayout_4"/>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QCheckBox" name="doNotShow_CheckBox">
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="toolTip">
        <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;When checked the Welcome window will not be displayed. You can change this setting in the SeamlyMe preferences.&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
       </property>
       <property name="text">
        <string>Do not show again</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDialogButtonBox" name="buttonBox">
       <property name="font">
        <font>
         <pointsize>10</pointsize>
         <bold>false</bold>
        </font>
       </property>
       <property name="autoFillBackground">
        <bool>true</bool>
       </property>
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="standardButtons">
        <set>QDialogButtonBox::Ok</set>
       </property>
       <property name="centerButtons">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../share/resources/seamlymeicon.qrc"/>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections/>
</ui>
