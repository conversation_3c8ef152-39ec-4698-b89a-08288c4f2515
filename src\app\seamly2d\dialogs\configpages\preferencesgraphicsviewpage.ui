<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PreferencesGraphicsViewPage</class>
 <widget class="QWidget" name="PreferencesGraphicsViewPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>485</width>
    <height>570</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>100</width>
    <height>100</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="font">
   <font>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string notr="true">Configuration</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTabWidget" name="graphicsViewTabWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>465</width>
       <height>550</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="font">
      <font>
       <pointsize>9</pointsize>
      </font>
     </property>
     <property name="cursor">
      <cursorShape>ArrowCursor</cursorShape>
     </property>
     <property name="autoFillBackground">
      <bool>false</bool>
     </property>
     <property name="tabPosition">
      <enum>QTabWidget::West</enum>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="appearanceTab">
      <attribute name="title">
       <string>Appearance</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_11">
       <item>
        <widget class="QGroupBox" name="toolbarsGroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>170</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Toolbars</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <widget class="QCheckBox" name="toolBarStyle_CheckBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Text label appears under the icon (recommended for beginners)</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>120</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
              <bold>true</bold>
             </font>
            </property>
            <property name="title">
             <string>Show tool toolbars</string>
            </property>
            <layout class="QGridLayout" name="gridLayout">
             <item row="0" column="0">
              <widget class="QCheckBox" name="toolsToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>ToolBox</string>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="QCheckBox" name="curveToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Curve</string>
               </property>
              </widget>
             </item>
             <item row="0" column="2">
              <widget class="QCheckBox" name="pieceToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Piece</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QCheckBox" name="pointToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Point</string>
               </property>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="QCheckBox" name="arcToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Arc</string>
               </property>
              </widget>
             </item>
             <item row="1" column="2">
              <widget class="QCheckBox" name="detailsToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Details</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QCheckBox" name="lineToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Line</string>
               </property>
              </widget>
             </item>
             <item row="2" column="1">
              <widget class="QCheckBox" name="operationToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Operations</string>
               </property>
              </widget>
             </item>
             <item row="2" column="2">
              <widget class="QCheckBox" name="layoutToolbar_CheckBox">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>100</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>9</pointsize>
                </font>
               </property>
               <property name="text">
                <string>Layout</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="dialogs_GgroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>270</height>
          </size>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Dialogs</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_25">
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_24">
            <item>
             <widget class="QCheckBox" name="useSecondMonitor_CheckBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>Open on second monitor if available</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="position_GroupBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>200</height>
               </size>
              </property>
              <property name="font">
               <font>
                <bold>true</bold>
               </font>
              </property>
              <property name="title">
               <string>Position</string>
              </property>
              <property name="checkable">
               <bool>false</bool>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_20">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_4" stretch="2,3">
                 <item>
                  <layout class="QVBoxLayout" name="verticalLayout_7">
                   <item>
                    <widget class="QRadioButton" name="ul_RadioButton">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="text">
                      <string>Top left</string>
                     </property>
                     <attribute name="buttonGroup">
                      <string notr="true">position_ButtonGroup</string>
                     </attribute>
                    </widget>
                   </item>
                   <item>
                    <widget class="QRadioButton" name="ur_RadioButton">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="text">
                      <string>Top right</string>
                     </property>
                     <attribute name="buttonGroup">
                      <string notr="true">position_ButtonGroup</string>
                     </attribute>
                    </widget>
                   </item>
                   <item>
                    <widget class="QRadioButton" name="center_RadioButton">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="text">
                      <string>Center</string>
                     </property>
                     <property name="checked">
                      <bool>true</bool>
                     </property>
                     <attribute name="buttonGroup">
                      <string notr="true">position_ButtonGroup</string>
                     </attribute>
                    </widget>
                   </item>
                   <item>
                    <widget class="QRadioButton" name="ll_RadioButton">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="text">
                      <string>Bottom left</string>
                     </property>
                     <attribute name="buttonGroup">
                      <string notr="true">position_ButtonGroup</string>
                     </attribute>
                    </widget>
                   </item>
                   <item>
                    <widget class="QRadioButton" name="lr_RadioButton">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="text">
                      <string>Bottom right</string>
                     </property>
                     <attribute name="buttonGroup">
                      <string notr="true">position_ButtonGroup</string>
                     </attribute>
                    </widget>
                   </item>
                   <item>
                    <widget class="QRadioButton" name="offset_RadioButton">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="text">
                      <string>Offset</string>
                     </property>
                     <attribute name="buttonGroup">
                      <string notr="true">position_ButtonGroup</string>
                     </attribute>
                    </widget>
                   </item>
                  </layout>
                 </item>
                 <item>
                  <layout class="QFormLayout" name="formLayout_11">
                   <property name="labelAlignment">
                    <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
                   </property>
                   <property name="formAlignment">
                    <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
                   </property>
                   <item row="0" column="0">
                    <widget class="QLabel" name="xOffset_Label">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>X Offset:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="0" column="1">
                    <widget class="QSpinBox" name="xOffset_SpinBox">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>50</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="autoFillBackground">
                      <bool>false</bool>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                     <property name="maximum">
                      <number>9999</number>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="0">
                    <widget class="QLabel" name="yOffset_Label">
                     <property name="minimumSize">
                      <size>
                       <width>60</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>Y Offset:</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item row="1" column="1">
                    <widget class="QSpinBox" name="yOffset_SpinBox">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>50</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="autoFillBackground">
                      <bool>false</bool>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                     </property>
                     <property name="maximum">
                      <number>9999</number>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="outputGroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>60</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Graphical output</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_10">
          <item>
           <widget class="QCheckBox" name="graphicsOutput_CheckBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Use anti-aliasing</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>129</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="fontsTab">
      <attribute name="title">
       <string>Fonts</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_19">
       <item>
        <widget class="QGroupBox" name="labelFont_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>420</width>
           <height>120</height>
          </size>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Label</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <layout class="QFormLayout" name="formLayout_2">
            <item row="0" column="0">
             <widget class="QLabel" name="labelFont_Label">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Font:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QFontComboBox" name="labelFont_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>20</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="label_Label">
            <property name="minimumSize">
             <size>
              <width>395</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>The quick brown fox jumps over the lazy dog</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="pointNameFont_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>420</width>
           <height>120</height>
          </size>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Point Names</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <item>
           <layout class="QFormLayout" name="formLayout_3">
            <item row="0" column="0">
             <widget class="QLabel" name="pointNameFont_Label">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Font:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QFontComboBox" name="pointNameFont_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>20</height>
               </size>
              </property>
              <property name="font">
               <font/>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="pointNameFontSize_Label">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Size:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QComboBox" name="pointNameFontSize_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="currentText">
               <string notr="true">96</string>
              </property>
              <property name="currentIndex">
               <number>28</number>
              </property>
              <item>
               <property name="text">
                <string notr="true">6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">8</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">9</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">10</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">10.5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">11</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">12</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">13</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">14</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">15</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">16</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">18</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">20</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">22</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">24</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">26</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">28</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">32</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">36</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">40</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">44</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">48</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">54</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">60</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">66</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">72</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">80</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">96</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="pointName_Label">
            <property name="minimumSize">
             <size>
              <width>395</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>The quick brown fox jumps over the lazy dog</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="pointNameFont_GroupBox_2">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>420</width>
           <height>120</height>
          </size>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>GUI</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_6">
          <item>
           <layout class="QFormLayout" name="formLayout_4">
            <item row="0" column="0">
             <widget class="QLabel" name="guiFont_Label">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Font:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QFontComboBox" name="guiFont_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>20</height>
               </size>
              </property>
              <property name="font">
               <font/>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="guiFontSize_Label">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Size:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QComboBox" name="guiFontSize_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>20</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="currentText">
               <string notr="true">32</string>
              </property>
              <property name="currentIndex">
               <number>18</number>
              </property>
              <item>
               <property name="text">
                <string notr="true">6</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">7</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">8</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">9</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">10</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">10.5</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">11</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">12</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">13</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">14</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">15</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">16</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">18</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">20</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">22</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">24</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">26</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">28</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">32</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">36</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">40</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">44</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">48</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">54</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">60</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">66</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">72</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">80</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string notr="true">96</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <widget class="QLabel" name="gui_Label">
            <property name="minimumSize">
             <size>
              <width>395</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>The quick brown fox jumps over the lazy dog</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_5">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>203</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="colorsTab">
      <attribute name="title">
       <string>Colors</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_18">
       <item>
        <widget class="QGroupBox" name="zoomRubberBandGroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Zoom Rubberband</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_12">
          <item>
           <layout class="QFormLayout" name="formLayout">
            <item row="0" column="0">
             <widget class="QLabel" name="zrbPositiveColorLabel">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Positive:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="ColorComboBox" name="zrbPositiveColor_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maxVisibleItems">
               <number>15</number>
              </property>
              <property name="iconSize">
               <size>
                <width>40</width>
                <height>14</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="zrbNegativeColorLabel">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Negative:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="ColorComboBox" name="zrbNegativeColor_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maxVisibleItems">
               <number>15</number>
              </property>
              <property name="iconSize">
               <size>
                <width>40</width>
                <height>14</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="pointNames_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Point Names</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_13">
          <item>
           <layout class="QFormLayout" name="formLayout_7">
            <item row="0" column="0">
             <widget class="QLabel" name="pontNameColor_Label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Default:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="ColorComboBox" name="pointNameColor_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maxVisibleItems">
               <number>15</number>
              </property>
              <property name="iconSize">
               <size>
                <width>40</width>
                <height>14</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="pontNameHoverColor_Label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Hover</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="ColorComboBox" name="pointNameHoverColor_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maxVisibleItems">
               <number>15</number>
              </property>
              <property name="iconSize">
               <size>
                <width>40</width>
                <height>14</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="drawing_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Drawing</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_14">
          <item>
           <layout class="QFormLayout" name="formLayout_8">
            <item row="0" column="0">
             <widget class="QLabel" name="axisOrginColor_Label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Axis Origin</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="ColorComboBox" name="axisOrginColor_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maxVisibleItems">
               <number>15</number>
              </property>
              <property name="iconSize">
               <size>
                <width>40</width>
                <height>14</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="selection_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Selection</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_22">
          <item>
           <layout class="QFormLayout" name="formLayout_9">
            <item row="0" column="0">
             <widget class="QLabel" name="primarySupportColor_Label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Primary:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="ColorComboBox" name="primarySupportColor_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maxVisibleItems">
               <number>15</number>
              </property>
              <property name="iconSize">
               <size>
                <width>40</width>
                <height>14</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="secondarySupportColor_Label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Secondary:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="ColorComboBox" name="secondarySupportColor_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maxVisibleItems">
               <number>15</number>
              </property>
              <property name="iconSize">
               <size>
                <width>40</width>
                <height>14</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="tertiarySupportColor_Laabel">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Tertiary:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="ColorComboBox" name="tertiarySupportColor_ComboBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="maxVisibleItems">
               <number>15</number>
              </property>
              <property name="iconSize">
               <size>
                <width>40</width>
                <height>14</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_4">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>282</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="navigationTab">
      <attribute name="title">
       <string>Navigation</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_17">
       <item>
        <widget class="QGroupBox" name="scrollBarGroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Scrollbars</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_9">
          <item>
           <widget class="QCheckBox" name="showScrollBars_CheckBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>14</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Show Scrollbars</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QFormLayout" name="formLayout_5">
            <item row="0" column="0">
             <widget class="QLabel" name="scrollBarWidthLabel">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>110</width>
                <height>14</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Width:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QSpinBox" name="scrollBarWidth_SpinBox">
              <property name="maximumSize">
               <size>
                <width>80</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
              <property name="suffix">
               <string> px</string>
              </property>
              <property name="minimum">
               <number>8</number>
              </property>
              <property name="maximum">
               <number>20</number>
              </property>
              <property name="value">
               <number>10</number>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="scrollDurationLabel">
              <property name="minimumSize">
               <size>
                <width>110</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Duration:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QSpinBox" name="scrollDuration_SpinBox">
              <property name="maximumSize">
               <size>
                <width>80</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="toolTip">
               <string>Scrolling animation duration</string>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
              <property name="suffix">
               <string comment="milliseconds"> ms</string>
              </property>
              <property name="minimum">
               <number>100</number>
              </property>
              <property name="maximum">
               <number>1000</number>
              </property>
              <property name="value">
               <number>300</number>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="scrollUpdateIntervalLabel">
              <property name="minimumSize">
               <size>
                <width>110</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Update interval:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="QSpinBox" name="scrollUpdateInterval_SpinBox">
              <property name="maximumSize">
               <size>
                <width>80</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="toolTip">
               <string>Time in milliseconds between each animation update</string>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
              <property name="suffix">
               <string comment="milliseconds"> ms</string>
              </property>
              <property name="minimum">
               <number>10</number>
              </property>
              <property name="maximum">
               <number>100</number>
              </property>
              <property name="value">
               <number>30</number>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_3">
              <property name="minimumSize">
               <size>
                <width>110</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Speed:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <layout class="QHBoxLayout" name="horizontalLayout">
              <item>
               <widget class="QLabel" name="label_5">
                <property name="maximumSize">
                 <size>
                  <width>10</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string notr="true">-</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QSlider" name="scrollSpeedFactor_Slider">
                <property name="minimumSize">
                 <size>
                  <width>225</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QSlider::groove:horizontal {
border: 1px solid #bbb;
background: white;
height: 10px;
border-radius: 4px;
}

QSlider::sub-page:horizontal {
background: qlineargradient(x1: 0, y1: 0,    x2: 0, y2: 1,
stop: 0 #66e, stop: 1 #bbf);
background: qlineargradient(x1: 0, y1: 0.2, x2: 1, y2: 1,
stop: 0 #bbf, stop: 1 #55f);
border: 1px solid #777;
height: 10px;
border-radius: 4px;
}

QSlider::add-page:horizontal {
background: #fff;
border: 1px solid #777;
height: 10px;
border-radius: 4px;
}

QSlider::handle:horizontal {
background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
stop:0 #eee, stop:1 #ccc);
border: 1px solid #777;
width: 13px;
margin-top: -2px;
margin-bottom: -2px;
border-radius: 4px;
}

QSlider::handle:horizontal:hover {
background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
stop:0 #fff, stop:1 #ddd);
border: 1px solid #444;
border-radius: 4px;
}

QSlider::sub-page:horizontal:disabled {
background: #bbb;
border-color: #999;
}

QSlider::add-page:horizontal:disabled {
background: #eee;
border-color: #999;
}

QSlider::handle:horizontal:disabled {
background: #eee;
border: 1px solid #aaa;
border-radius: 4px;
}</string>
                </property>
                <property name="minimum">
                 <number>10</number>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="value">
                 <number>20</number>
                </property>
                <property name="sliderPosition">
                 <number>20</number>
                </property>
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_4">
                <property name="maximumSize">
                 <size>
                  <width>10</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string notr="true">+</string>
                </property>
                <property name="indent">
                 <number>0</number>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="zoomGroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Zoom</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_8">
          <item>
           <widget class="QCheckBox" name="zoomModKey_CheckBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>14</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Use CTRL modifier</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QFormLayout" name="formLayout_6">
            <item row="0" column="0">
             <widget class="QLabel" name="label">
              <property name="minimumSize">
               <size>
                <width>110</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Speed:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <item>
               <widget class="QLabel" name="label_2">
                <property name="maximumSize">
                 <size>
                  <width>10</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string notr="true">-</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
                <property name="indent">
                 <number>0</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QSlider" name="zoomSpeedFactor_Slider">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>225</width>
                  <height>22</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QSlider::groove:horizontal {
border: 1px solid #bbb;
background: white;
height: 10px;
border-radius: 4px;
}

QSlider::sub-page:horizontal {
background: qlineargradient(x1: 0, y1: 0,    x2: 0, y2: 1,
stop: 0 #66e, stop: 1 #bbf);
background: qlineargradient(x1: 0, y1: 0.2, x2: 1, y2: 1,
stop: 0 #bbf, stop: 1 #55f);
border: 1px solid #777;
height: 10px;
border-radius: 4px;
}

QSlider::add-page:horizontal {
background: #fff;
border: 1px solid #777;
height: 10px;
border-radius: 4px;
}

QSlider::handle:horizontal {
background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
stop:0 #eee, stop:1 #ccc);
border: 1px solid #777;
width: 13px;
margin-top: -2px;
margin-bottom: -2px;
border-radius: 4px;
}

QSlider::handle:horizontal:hover {
background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
stop:0 #fff, stop:1 #ddd);
border: 1px solid #444;
border-radius: 4px;
}

QSlider::sub-page:horizontal:disabled {
background: #bbb;
border-color: #999;
}

QSlider::add-page:horizontal:disabled {
background: #eee;
border-color: #999;
}

QSlider::handle:horizontal:disabled {
background: #eee;
border: 1px solid #aaa;
border-radius: 4px;
}</string>
                </property>
                <property name="maximum">
                 <number>32</number>
                </property>
                <property name="sliderPosition">
                 <number>16</number>
                </property>
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_6">
                <property name="maximumSize">
                 <size>
                  <width>10</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string notr="true">+</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_3">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>264</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="behaviorTab">
      <attribute name="title">
       <string>Behavior</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_16">
       <item>
        <widget class="QGroupBox" name="constaints_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Constraints</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <widget class="QCheckBox" name="constrainModKey_CheckBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>18</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>18</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Use CTRL modifier</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="QLabel" name="constainValueLabel">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Angle Step:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QDoubleSpinBox" name="constrainValue_DoubleSpinBox">
              <property name="minimumSize">
               <size>
                <width>90</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
              <property name="suffix">
               <string> deg</string>
              </property>
              <property name="minimum">
               <double>5.000000000000000</double>
              </property>
              <property name="maximum">
               <double>60.000000000000000</double>
              </property>
              <property name="singleStep">
               <double>5.000000000000000</double>
              </property>
              <property name="value">
               <double>10.000000000000000</double>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="zoom_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Zoom</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_15">
          <item>
           <widget class="QCheckBox" name="zoomDoubleClick_CheckBox">
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Zoom to selected with double click</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="panActiveSpacePressed_CheckBox">
            <property name="font">
             <font>
              <pointsize>9</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Pan active while Space key is pressed</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="export_GroupBox">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>9</pointsize>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Export</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_21">
          <item>
           <layout class="QFormLayout" name="formLayout_10">
            <item row="0" column="0">
             <widget class="QLabel" name="quality_Label">
              <property name="minimumSize">
               <size>
                <width>85</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>9</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Quality:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <layout class="QHBoxLayout" name="horizontalLayout_6">
              <item>
               <widget class="QLabel" name="label_12">
                <property name="maximumSize">
                 <size>
                  <width>10</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string notr="true">-</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                </property>
                <property name="indent">
                 <number>0</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QSlider" name="quality_Slider">
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>225</width>
                  <height>22</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>225</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="styleSheet">
                 <string notr="true">QSlider::groove:horizontal {
border: 1px solid #bbb;
background: white;
height: 10px;
border-radius: 4px;
}

QSlider::sub-page:horizontal {
background: qlineargradient(x1: 0, y1: 0,    x2: 0, y2: 1,
stop: 0 #66e, stop: 1 #bbf);
background: qlineargradient(x1: 0, y1: 0.2, x2: 1, y2: 1,
stop: 0 #bbf, stop: 1 #55f);
border: 1px solid #777;
height: 10px;
border-radius: 4px;
}

QSlider::add-page:horizontal {
background: #fff;
border: 1px solid #777;
height: 10px;
border-radius: 4px;
}

QSlider::handle:horizontal {
background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
stop:0 #eee, stop:1 #ccc);
border: 1px solid #777;
width: 13px;
margin-top: -2px;
margin-bottom: -2px;
border-radius: 4px;
}

QSlider::handle:horizontal:hover {
background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
stop:0 #fff, stop:1 #ddd);
border: 1px solid #444;
border-radius: 4px;
}

QSlider::sub-page:horizontal:disabled {
background: #bbb;
border-color: #999;
}

QSlider::add-page:horizontal:disabled {
background: #eee;
border-color: #999;
}

QSlider::handle:horizontal:disabled {
background: #eee;
border: 1px solid #aaa;
border-radius: 4px;
}</string>
                </property>
                <property name="maximum">
                 <number>100</number>
                </property>
                <property name="value">
                 <number>75</number>
                </property>
                <property name="sliderPosition">
                 <number>75</number>
                </property>
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_13">
                <property name="maximumSize">
                 <size>
                  <width>10</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <pointsize>9</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string notr="true">+</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="pen_GroupBox">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="font">
          <font>
           <bold>true</bold>
          </font>
         </property>
         <property name="title">
          <string>Pen</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_23">
          <item>
           <widget class="QCheckBox" name="useCurrentPen_checkBox">
            <property name="text">
             <string>Always use current pen</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QCheckBox" name="showOnlyIso_CheckBox">
            <property name="text">
             <string>Show only ISO line weights in drop down boxes</string>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>376</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>ColorComboBox</class>
   <extends>QComboBox</extends>
   <header location="global">color_combobox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
 <buttongroups>
  <buttongroup name="position_ButtonGroup"/>
 </buttongroups>
</ui>
