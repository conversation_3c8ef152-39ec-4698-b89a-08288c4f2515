<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>About2DAppDialog</class>
 <widget class="QDialog" name="About2DAppDialog">
  <property name="windowModality">
   <enum>Qt::ApplicationModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>620</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>600</width>
    <height>620</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="cursor">
   <cursorShape>ArrowCursor</cursorShape>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::NoContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>About Seamly2D</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/logos/seamly_logo_32.png</normaloff>:/icon/logos/seamly_logo_32.png</iconset>
  </property>
  <property name="locale">
   <locale language="English" country="UnitedStates"/>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <property name="spacing">
      <number>4</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>9</number>
     </property>
     <item>
      <widget class="QLabel" name="label">
       <property name="minimumSize">
        <size>
         <width>60</width>
         <height>0</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="pixmap">
        <pixmap resource="../../../libs/vmisc/share/resources/icon.qrc">:/icon/logos/seamly2d_vertical.png</pixmap>
       </property>
       <property name="scaledContents">
        <bool>false</bool>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QTabWidget" name="tabWidget">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="currentIndex">
          <number>1</number>
         </property>
         <widget class="QWidget" name="about_tab">
          <attribute name="title">
           <string>About</string>
          </attribute>
          <layout class="QVBoxLayout" name="verticalLayout_9">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <item>
              <widget class="QToolButton" name="copyToClipbaord_toolButton">
               <property name="toolTip">
                <string>Copy build data to clipboard</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="icon">
                <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                 <normaloff>:/icon/32x32/clipboard_icon.png</normaloff>:/icon/32x32/clipboard_icon.png</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>32</width>
                 <height>32</height>
                </size>
               </property>
              </widget>
             </item>
             <item>
              <widget class="Line" name="line_2">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_2">
               <property name="font">
                <font>
                 <pointsize>24</pointsize>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>Seamly 2D</string>
               </property>
              </widget>
             </item>
             <item>
              <layout class="QGridLayout" name="gridLayout">
               <item row="0" column="0">
                <widget class="QLabel" name="seamly2D_Version_label">
                 <property name="minimumSize">
                  <size>
                   <width>120</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="text">
                  <string>Version:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="0" column="1">
                <widget class="QLabel" name="version_value">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">Version</string>
                 </property>
                </widget>
               </item>
               <item row="1" column="0">
                <widget class="QLabel" name="buildRevision_label">
                 <property name="minimumSize">
                  <size>
                   <width>120</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                   <bold>false</bold>
                  </font>
                 </property>
                 <property name="accessibleName">
                  <string/>
                 </property>
                 <property name="accessibleDescription">
                  <string/>
                 </property>
                 <property name="text">
                  <string notr="true">Build revision:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="1" column="1">
                <widget class="QLabel" name="revision_value">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">Revision</string>
                 </property>
                </widget>
               </item>
               <item row="2" column="0">
                <widget class="QLabel" name="buildDate_label">
                 <property name="minimumSize">
                  <size>
                   <width>120</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Build date:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="2" column="1">
                <widget class="QLabel" name="buildDate_value">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">Build date</string>
                 </property>
                </widget>
               </item>
               <item row="3" column="0">
                <widget class="QLabel" name="qtVersion_label">
                 <property name="minimumSize">
                  <size>
                   <width>120</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Qt Version:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="3" column="1">
                <widget class="QLabel" name="qtVersion_value">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">QT_Version</string>
                 </property>
                </widget>
               </item>
               <item row="4" column="0">
                <widget class="QLabel" name="cpu_label">
                 <property name="minimumSize">
                  <size>
                   <width>120</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">CPU:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="4" column="1">
                <widget class="QLabel" name="cpu_value">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">CPU</string>
                 </property>
                </widget>
               </item>
               <item row="5" column="0">
                <widget class="QLabel" name="compiler_label">
                 <property name="minimumSize">
                  <size>
                   <width>120</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Compiler:</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item row="5" column="1">
                <widget class="QLabel" name="compiler_value">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string notr="true">Compiler</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="Line" name="line">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="legalStuff_label">
               <property name="font">
                <font>
                 <pointsize>10</pointsize>
                </font>
               </property>
               <property name="text">
                <string notr="true">label_Legal_Stuff</string>
               </property>
               <property name="wordWrap">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="verticalSpacer">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_Web_Site">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="palette">
              <palette>
               <active>
                <colorrole role="ButtonText">
                 <brush brushstyle="SolidPattern">
                  <color alpha="255">
                   <red>0</red>
                   <green>0</green>
                   <blue>255</blue>
                  </color>
                 </brush>
                </colorrole>
               </active>
               <inactive>
                <colorrole role="ButtonText">
                 <brush brushstyle="SolidPattern">
                  <color alpha="255">
                   <red>0</red>
                   <green>0</green>
                   <blue>255</blue>
                  </color>
                 </brush>
                </colorrole>
               </inactive>
               <disabled>
                <colorrole role="ButtonText">
                 <brush brushstyle="SolidPattern">
                  <color alpha="255">
                   <red>120</red>
                   <green>120</green>
                   <blue>120</blue>
                  </color>
                 </brush>
                </colorrole>
               </disabled>
              </palette>
             </property>
             <property name="font">
              <font>
               <pointsize>11</pointsize>
               <underline>true</underline>
              </font>
             </property>
             <property name="cursor">
              <cursorShape>PointingHandCursor</cursorShape>
             </property>
             <property name="text">
              <string notr="true">Seamly Web Site</string>
             </property>
             <property name="autoDefault">
              <bool>false</bool>
             </property>
             <property name="flat">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="system_tab">
          <attribute name="title">
           <string>System</string>
          </attribute>
          <layout class="QVBoxLayout" name="verticalLayout_8">
           <item>
            <widget class="QScrollArea" name="scrollArea">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>400</height>
              </size>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <widget class="QWidget" name="scrollAreaWidgetContents">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>463</width>
                <height>599</height>
               </rect>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_10">
               <item>
                <widget class="QGroupBox" name="groupBox">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="title">
                  <string>Version</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_7">
                  <item>
                   <layout class="QGridLayout" name="gridLayout_5">
                    <item row="2" column="1">
                     <widget class="QLabel" name="sysBuildDate_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="3" column="1">
                     <widget class="QLabel" name="sysCPU_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="0">
                     <widget class="QLabel" name="sysBuildDate_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Build date:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <widget class="QLabel" name="sysRevision_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Revision:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="4" column="0">
                     <widget class="QLabel" name="sysCompiler_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Compiler:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="4" column="1">
                     <widget class="QLabel" name="sysCompiler_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="3" column="0">
                     <widget class="QLabel" name="sysCPU_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">CPU:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="1">
                     <widget class="QLabel" name="sysRevision_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0">
                     <widget class="QLabel" name="sysVersion_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Version:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="1">
                     <widget class="QLabel" name="sysVersion_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QGroupBox" name="groupBox_2">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="title">
                  <string>Architecture</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_5">
                  <property name="leftMargin">
                   <number>6</number>
                  </property>
                  <property name="topMargin">
                   <number>3</number>
                  </property>
                  <property name="rightMargin">
                   <number>6</number>
                  </property>
                  <property name="bottomMargin">
                   <number>6</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_3">
                    <item row="0" column="0">
                     <widget class="QLabel" name="sysArchCPU_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">CPU:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="1">
                     <widget class="QLabel" name="sysArchCPU_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="0">
                     <widget class="QLabel" name="sysOS_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">OS:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="1">
                     <widget class="QLabel" name="sysOS_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="0">
                     <widget class="QLabel" name="sysOSVersion_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>OS Version:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="1">
                     <widget class="QLabel" name="sysOSVersion_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QGroupBox" name="groupBox_3">
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="title">
                  <string>Locale</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_6">
                  <property name="leftMargin">
                   <number>6</number>
                  </property>
                  <property name="topMargin">
                   <number>3</number>
                  </property>
                  <property name="rightMargin">
                   <number>6</number>
                  </property>
                  <property name="bottomMargin">
                   <number>6</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_4">
                    <item row="1" column="0">
                     <widget class="QLabel" name="sysLocaleCountry_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Country:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="1">
                     <widget class="QLabel" name="sysLocalLang_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="5" column="1">
                     <widget class="QLabel" name="sysLocalNegative_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="1">
                     <widget class="QLabel" name="sysLocale_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="6" column="1">
                     <widget class="QLabel" name="sysLocalPositive_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="7" column="1">
                     <widget class="QLabel" name="sysLocalDirection_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="4" column="0">
                     <widget class="QLabel" name="sysLocalDecimal_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Decimal point:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="3" column="0">
                     <widget class="QLabel" name="sysLocalScriptName_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Script name:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="8" column="0">
                     <widget class="QLabel" name="sysCodec_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>System codec:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="1" column="1">
                     <widget class="QLabel" name="sysLocaleCountry_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="2" column="0">
                     <widget class="QLabel" name="sysLocalLang_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Language:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="7" column="0">
                     <widget class="QLabel" name="sysLocalDirection_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Text direction:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="0">
                     <widget class="QLabel" name="sysLocale_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Name:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="8" column="1">
                     <widget class="QLabel" name="sysCodec_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="3" column="1">
                     <widget class="QLabel" name="sysLocalScriptName_vale">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="4" column="1">
                     <widget class="QLabel" name="sysLocalDecimal_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                     </widget>
                    </item>
                    <item row="6" column="0">
                     <widget class="QLabel" name="sysLocalPositive_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Positive sign:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="5" column="0">
                     <widget class="QLabel" name="sysLocalNegative_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Negative sign:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QGroupBox" name="groupBox_4">
                 <property name="font">
                  <font>
                   <pointsize>12</pointsize>
                   <bold>true</bold>
                  </font>
                 </property>
                 <property name="title">
                  <string>Application</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_4">
                  <property name="leftMargin">
                   <number>6</number>
                  </property>
                  <property name="topMargin">
                   <number>3</number>
                  </property>
                  <property name="rightMargin">
                   <number>6</number>
                  </property>
                  <property name="bottomMargin">
                   <number>6</number>
                  </property>
                  <item>
                   <layout class="QGridLayout" name="gridLayout_2">
                    <item row="0" column="0">
                     <widget class="QLabel" name="sysArguments_label">
                      <property name="minimumSize">
                       <size>
                        <width>100</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string>Arguments:</string>
                      </property>
                      <property name="alignment">
                       <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                      </property>
                     </widget>
                    </item>
                    <item row="0" column="1">
                     <widget class="QLabel" name="sysArguments_value">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>300</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <pointsize>9</pointsize>
                        <bold>false</bold>
                       </font>
                      </property>
                      <property name="text">
                       <string notr="true">TextLabel</string>
                      </property>
                      <property name="wordWrap">
                       <bool>true</bool>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <spacer name="verticalSpacer_2">
                 <property name="orientation">
                  <enum>Qt::Vertical</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>20</width>
                   <height>40</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QWidget" name="contributors_tab">
          <attribute name="title">
           <string>Contributors</string>
          </attribute>
          <layout class="QVBoxLayout" name="verticalLayout_12">
           <item>
            <widget class="QScrollArea" name="scrollArea_2">
             <property name="verticalScrollBarPolicy">
              <enum>Qt::ScrollBarAlwaysOff</enum>
             </property>
             <property name="widgetResizable">
              <bool>true</bool>
             </property>
             <widget class="QWidget" name="scrollAreaWidgetContents_2">
              <property name="geometry">
               <rect>
                <x>0</x>
                <y>0</y>
                <width>480</width>
                <height>516</height>
               </rect>
              </property>
              <property name="sizePolicy">
               <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_11">
               <item>
                <widget class="QPlainTextEdit" name="plainTextEdit_Contributors_2">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <pointsize>11</pointsize>
                  </font>
                 </property>
                 <property name="autoFillBackground">
                  <bool>false</bool>
                 </property>
                 <property name="verticalScrollBarPolicy">
                  <enum>Qt::ScrollBarAsNeeded</enum>
                 </property>
                 <property name="horizontalScrollBarPolicy">
                  <enum>Qt::ScrollBarAlwaysOff</enum>
                 </property>
                 <property name="readOnly">
                  <bool>true</bool>
                 </property>
                 <property name="plainText">
                  <string notr="true">Project founders:
  Roman Telezhynskyi &lt;<EMAIL>&gt;
  Susan Spencer &lt;<EMAIL>&gt;

Project maintainer, Product designer, Prototype developer, Community manager:
  Susan Spencer &lt;<EMAIL>&gt;

Primary code developer:
  Douglas Caskey

User community moderators at https://forum.seamly.io:
  @Grace
  @Pneumarian

Contributors (alphabetical order):
  Abhijith Subhash
  Aleenejejw
  Aleksey Zaharov
  Alex Lopez Zubieta
  Alex Zaharov
  Alexsey Schmuley
  Araceli Yanez
  AstHrO
  Benjamin Nauk
  Bernd Zeimetz
  Bettina Gatzlaff
  Bojan Kverh
  Christine Neupert
  Christoph Settgast
  Dan McMahill
  Douglas Caskey
  Evans Perret
  Evgeniy Strelnikov
  Fabrice Salvaire
  Felix Ulber
  Frukto
  Grace de Man
  Harshil Patel
  H. Naehmann - German translations
  Ian Cunningham
  Jackstr
  Jim Wouda
  Johannes Brakensiek
  Keepy38
  Kim March-Force
  Lindsay Williams
  Marius Ghita
  Martijn Kleptog
  Mischa Linus Krempel
  Mo Odling
  Mohammed Ibrahim
  Oni303
  Patrick Proy
  Peter Gsellmann
  Pneumarian
  QtGirl
  Raina Gufstafson
  Roman Postanciuk
  Ronan LeTiec
  Sabine Schmaltz
  Sergey Alyoshin
  SS-Adam
  Steve Conklin
  Subhash Abhijith
  Ths80
  Timo Virtaneva
  U38cg
  Valentina Zhuravska
  Vincent Bermel
</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <widget class="QPushButton" name="pushButtonCheckUpdate">
       <property name="text">
        <string>Check For Updates</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QProgressBar" name="downloadProgress">
       <property name="value">
        <number>25</number>
       </property>
       <property name="alignment">
        <set>Qt::AlignCenter</set>
       </property>
       <property name="textVisible">
        <bool>true</bool>
       </property>
       <property name="invertedAppearance">
        <bool>false</bool>
       </property>
       <property name="format">
        <string>Downloading installer %p% complete</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QDialogButtonBox" name="buttonBox">
       <property name="autoFillBackground">
        <bool>true</bool>
       </property>
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="standardButtons">
        <set>QDialogButtonBox::Ok</set>
       </property>
       <property name="centerButtons">
        <bool>false</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>About2DAppDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>248</x>
     <y>254</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>About2DAppDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>316</x>
     <y>260</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
