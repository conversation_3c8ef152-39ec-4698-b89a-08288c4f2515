IDI_ICON1               ICON    DISCARDABLE     "../../../../libs/vmisc/share/resources/icon/64x64/icon64x64.ico"

#include <windows.h>
#include "../../version.h"

VS_VERSION_INFO VERSIONINFO
FILEVERSION     VER_FILEVERSION
PRODUCTVERSION  VER_PRODUCTVERSION

#ifdef V_PRERELEASE

#ifdef V_NO_DEBUG
    FILEFLAGS (VS_FF_PRERELEASE)
#else
    FILEFLAGS (VS_FF_DEBUG|VS_FF_PRERELEASE)
#endif

#else

#ifdef V_NO_DEBUG
    FILEFLAGS (VS_FF_NORMAL)
#else
    FILEFLAGS (VS_FF_DEBUG|VS_FF_NORMAL)
#endif

#endif

FILEOS         	VOS__WINDOWS32
FILETYPE       	VFT_APP
FILESUBTYPE    	VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904E4"
        BEGIN
            VALUE "CompanyName",        VER_COMPANYNAME_STR
            VALUE "FileDescription",    VER_FILEDESCRIPTION_STR
            VALUE "FileVersion",        VER_FILEVERSION_STR
            VALUE "InternalName",       VER_INTERNALNAME_STR
            VALUE "LegalCopyright",     VER_LEGALCOPYRIGHT_STR
            VALUE "LegalTrademarks1",   VER_LEGALTRADEMARKS1_STR
            VALUE "LegalTrademarks2",   VER_LEGALTRADEMARKS2_STR
            VALUE "OriginalFilename",   VER_ORIGINALFILENAME_STR
            VALUE "ProductName",        VER_PRODUCTNAME_STR
            VALUE "ProductVersion",     VER_PRODUCTVERSION_STR
        END
    END

    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x0409, 0x04E4 //U.S. English
    END
END
