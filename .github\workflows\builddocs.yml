name: BuildDocs

# for a description, see README.md in this folder
# use this file for testing Doxygen
on:
  workflow_dispatch:

jobs:

  document:
    runs-on: ubuntu-latest

    steps:
      - name: Doxygen GitHub Pages Deploy Action
        uses: DenverCoder1/doxygen-github-pages-action@v1.3.0
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: gh-pages
          # folder where docs are built, must match OUTPUT_DIRECTORY in Doxyfile + html
          folder: docs/html
          config_file: Doxyfile
          # target_folder must match pages config in https://github.com/FashionFreedom/Seamly2D/settings/pages
          target_folder: docs/

