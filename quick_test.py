from seamly2d_ai_designer import TShirt

print("🧪 快速测试开始...")

measurements = {
    "chest_width": 50.0,
    "shoulder_width": 15.0, 
    "length": 70.0,
    "neck_width": 20.0
}

tshirt = TShirt()
pattern_file = (tshirt
               .set_measurements(measurements)
               .set_ease(3.0)
               .create_pattern("output/quick_test.sm2d", preview=True))

print(f"✅ 快速测试成功! 文件: {pattern_file}") 