<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ShortcutsDialog</class>
 <widget class="QDialog" name="ShortcutsDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>774</width>
    <height>640</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Seamly2D Shortcuts</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
    <normaloff>:/icon/logos/seamly_logo_32.png</normaloff>:/icon/logos/seamly_logo_32.png</iconset>
  </property>
  <property name="sizeGripEnabled">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_4">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QToolButton" name="Icon">
         <property name="minimumSize">
          <size>
           <width>128</width>
           <height>94</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>128</width>
           <height>128</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">border: none;background-color:transparent;</string>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/2d_shortcuts.png</normaloff>:/icon/2d_shortcuts.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>128</width>
           <height>94</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QToolButton" name="toolButton">
         <property name="minimumSize">
          <size>
           <width>128</width>
           <height>31</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>128</width>
           <height>31</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">border: none;background-color:transparent;</string>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/BuiltWithQt.png</normaloff>:/icon/BuiltWithQt.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>128</width>
           <height>31</height>
          </size>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QFrame" name="frame">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>40</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(255, 255, 255);</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::Box</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QToolButton" name="clipboard_ToolButton">
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="toolTip">
               <string>Copy shortcuts to the clipboard</string>
              </property>
              <property name="autoFillBackground">
               <bool>false</bool>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                <normaloff>:/icon/32x32/clipboard_icon.png</normaloff>:/icon/32x32/clipboard_icon.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QToolButton" name="pdf_ToolButton">
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="toolTip">
               <string>Export shortcuts as a PDF</string>
              </property>
              <property name="text">
               <string notr="true">...</string>
              </property>
              <property name="icon">
               <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                <normaloff>:/icon/32x32/pdf_icon.png</normaloff>:/icon/32x32/pdf_icon.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QToolButton" name="printer_ToolButton">
              <property name="maximumSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="toolTip">
               <string>Send shortcuts to the Printer</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
                <normaloff>:/icon/32x32/printer_icon.png</normaloff>:/icon/32x32/printer_icon.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QTextBrowser" name="shortcuts_TextBrowser">
         <property name="palette">
          <palette>
           <active>
            <colorrole role="Button">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Base">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Window">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
           </active>
           <inactive>
            <colorrole role="Button">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Base">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Window">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
           </inactive>
           <disabled>
            <colorrole role="Button">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Base">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Window">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>255</green>
               <blue>255</blue>
              </color>
             </brush>
            </colorrole>
           </disabled>
          </palette>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: rgb(255, 255, 255);</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::Box</enum>
         </property>
         <property name="documentTitle">
          <string/>
         </property>
         <property name="markdown">
          <string notr="true"/>
         </property>
         <property name="html">
          <string notr="true">&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;meta charset=&quot;utf-8&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
hr { height: 1px; border-width: 0; }
li.unchecked::marker { content: &quot;\2610&quot;; }
li.checked::marker { content: &quot;\2612&quot;; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-size:9pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Close</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>clicked(QAbstractButton*)</signal>
   <receiver>ShortcutsDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>227</x>
     <y>349</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
