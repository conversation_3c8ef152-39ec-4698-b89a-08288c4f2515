"""
配置类模块，提供版型生成器的配置管理
"""

import json
import os
from typing import Dict, Any, Optional

class PatternConfig:
    """版型配置类"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化配置
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        # 默认配置
        self.unit = "cm"
        self.seamly2d_version = "0.6.8"
        self.default_ease = 2.0  # 默认放松量(cm)
        self.curve_precision = 100  # 曲线精度
        self.label_offset = {"mx": "0.132292", "my": "0.264583"}
        self.line_types = {
            "normal": "hair",
            "dashed": "dashLine",
            "dotted": "dotLine",
            "dash_dot": "dashDotLine"
        }
        
        # 如果提供了配置文件，则加载它
        if config_file and os.path.exists(config_file):
            self.load_from_file(config_file)
    
    def load_from_file(self, config_file: str) -> None:
        """从配置文件加载配置
        
        Args:
            config_file: 配置文件路径
        """
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            # 更新配置
            for key, value in config_data.items():
                if hasattr(self, key):
                    setattr(self, key, value)
                    
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
    
    def save_to_file(self, config_file: str) -> None:
        """保存配置到文件
        
        Args:
            config_file: 配置文件路径
        """
        config_data = {
            "unit": self.unit,
            "seamly2d_version": self.seamly2d_version,
            "default_ease": self.default_ease,
            "curve_precision": self.curve_precision,
            "label_offset": self.label_offset,
            "line_types": self.line_types
        }
        
        try:
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
                
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
    
    @classmethod
    def from_file(cls, config_file: str) -> 'PatternConfig':
        """从配置文件创建配置对象
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            PatternConfig: 配置对象
        """
        return cls(config_file) 