<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>PiecesWidget</class>
 <widget class="QWidget" name="PiecesWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>270</width>
    <height>338</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>270</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,1,0,0">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>1</number>
         </property>
         <item>
          <widget class="QToolButton" name="includeAllPieces_ToolButton">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="toolTip">
            <string>Include all pieces</string>
           </property>
           <property name="statusTip">
            <string>Include all pieces</string>
           </property>
           <property name="icon">
            <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
             <normaloff>:/icon/32x32/visible_on.png</normaloff>:/icon/32x32/visible_on.png</iconset>
           </property>
           <property name="autoRaise">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QToolButton" name="invertIncludedPieces_ToolButton">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="toolTip">
            <string>Invert included pieces</string>
           </property>
           <property name="statusTip">
            <string>Invert included pieces</string>
           </property>
           <property name="icon">
            <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
             <normaloff>:/icon/32x32/invert_selection.png</normaloff>:/icon/32x32/invert_selection.png</iconset>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QToolButton" name="excludeAllPieces_ToolButton">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="toolTip">
            <string>Exclude all pieces</string>
           </property>
           <property name="statusTip">
            <string>Exclude all pieces</string>
           </property>
           <property name="icon">
            <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
             <normaloff>:/icon/32x32/visible_off.png</normaloff>:/icon/32x32/visible_off.png</iconset>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0,0,0">
         <property name="spacing">
          <number>1</number>
         </property>
         <item>
          <widget class="QToolButton" name="lockAllPieces_ToolButton">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="toolTip">
            <string>Lock all pieces</string>
           </property>
           <property name="statusTip">
            <string>Lock all pieces</string>
           </property>
           <property name="icon">
            <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
             <normaloff>:/icon/32x32/lock_on.png</normaloff>:/icon/32x32/lock_on.png</iconset>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QToolButton" name="invertLockedPieces_ToolButton">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="toolTip">
            <string>Invert locked pieces</string>
           </property>
           <property name="statusTip">
            <string>Invert locked pieces</string>
           </property>
           <property name="icon">
            <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
             <normaloff>:/icon/32x32/invert_selection.png</normaloff>:/icon/32x32/invert_selection.png</iconset>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QToolButton" name="unlockAllPieces_ToolButton">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="toolTip">
            <string>Unlock all pieces</string>
           </property>
           <property name="statusTip">
            <string>Unlock all pieces</string>
           </property>
           <property name="icon">
            <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
             <normaloff>:/icon/32x32/lock_off.png</normaloff>:/icon/32x32/lock_off.png</iconset>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QToolButton" name="editColor_ToolButton">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="toolTip">
          <string>Select Color</string>
         </property>
         <property name="statusTip">
          <string>Select Color</string>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/32x32/color_picker.png</normaloff>:/icon/32x32/color_picker.png</iconset>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QToolButton" name="editPiece_ToolButton">
         <property name="enabled">
          <bool>true</bool>
         </property>
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="toolTip">
          <string>Edit pattern piece properties</string>
         </property>
         <property name="statusTip">
          <string>Edit pattern piece properties</string>
         </property>
         <property name="icon">
          <iconset resource="../../../libs/vmisc/share/resources/icon.qrc">
           <normaloff>:/icon/32x32/edit.png</normaloff>:/icon/32x32/edit.png</iconset>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QTableWidget" name="tableWidget">
       <property name="palette">
        <palette>
         <active>
          <colorrole role="Highlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>160</red>
             <green>160</green>
             <blue>160</blue>
            </color>
           </brush>
          </colorrole>
         </active>
         <inactive>
          <colorrole role="Highlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>160</red>
             <green>160</green>
             <blue>160</blue>
            </color>
           </brush>
          </colorrole>
         </inactive>
         <disabled>
          <colorrole role="Highlight">
           <brush brushstyle="SolidPattern">
            <color alpha="255">
             <red>0</red>
             <green>120</green>
             <blue>215</blue>
            </color>
           </brush>
          </colorrole>
         </disabled>
        </palette>
       </property>
       <property name="alternatingRowColors">
        <bool>false</bool>
       </property>
       <property name="selectionMode">
        <enum>QAbstractItemView::SingleSelection</enum>
       </property>
       <property name="selectionBehavior">
        <enum>QAbstractItemView::SelectRows</enum>
       </property>
       <property name="iconSize">
        <size>
         <width>16</width>
         <height>16</height>
        </size>
       </property>
       <property name="textElideMode">
        <enum>Qt::ElideNone</enum>
       </property>
       <property name="sortingEnabled">
        <bool>false</bool>
       </property>
       <property name="columnCount">
        <number>5</number>
       </property>
       <attribute name="horizontalHeaderMinimumSectionSize">
        <number>16</number>
       </attribute>
       <attribute name="horizontalHeaderDefaultSectionSize">
        <number>30</number>
       </attribute>
       <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
        <bool>true</bool>
       </attribute>
       <attribute name="horizontalHeaderStretchLastSection">
        <bool>true</bool>
       </attribute>
       <attribute name="verticalHeaderVisible">
        <bool>false</bool>
       </attribute>
       <attribute name="verticalHeaderMinimumSectionSize">
        <number>10</number>
       </attribute>
       <attribute name="verticalHeaderHighlightSections">
        <bool>false</bool>
       </attribute>
       <column/>
       <column/>
       <column/>
       <column/>
       <column/>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../../libs/vmisc/share/resources/icon.qrc"/>
 </resources>
 <connections/>
</ui>
