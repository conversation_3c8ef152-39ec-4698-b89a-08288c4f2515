/************************************************************************
 **
 **  @file   vgraphicsfillitem.h
 **  <AUTHOR>
 **  @date   October 16, 2016
 **
 **  @brief
 **  @copyright
 **  This source code is part of the Valentine project, a pattern making
 **  program, whose allow create and modeling patterns of clothing.
 **  Copyright (C) 2013-2015 Seamly2D project
 **  <https://github.com/fashionfreedom/seamly2d> All Rights Reserved.
 **
 **  Seamly2D is free software: you can redistribute it and/or modify
 **  it under the terms of the GNU General Public License as published by
 **  the Free Software Foundation, either version 3 of the License, or
 **  (at your option) any later version.
 **
 **  Seamly2D is distributed in the hope that it will be useful,
 **  but WITHOUT ANY WARRANTY; without even the implied warranty of
 **  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 **  GNU General Public License for more details.
 **
 **  You should have received a copy of the GNU General Public License
 **  along with Seamly2D.  If not, see <http://www.gnu.org/licenses/>.
 **
 *************************************************************************/

#ifndef VGRAPHICSFILLITEM_H
#define VGRAPHICSFILLITEM_H

#include <QGraphicsPathItem>
#include <QPainter>

class VGraphicsFillItem : public QGraphicsPathItem
{
public:
    /**
     * @brief VGraphicsFillItem Constructor
     * @param color color used for the item's pen & brush
     * @param fill flag used if the item is to be filled or not
     */
    explicit VGraphicsFillItem(const QColor &color, bool fill, QGraphicsItem *parent = nullptr);
    /**
     * @brief ~VGraphicsFillItem Destructor
     */
    ~VGraphicsFillItem();
    /**
     * @brief paint Paints the item, filling the inside surface
     * @param painter pointer to the painter object
     * @param option unused
     * @param widget unused
     */
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget);

private:
    QColor   m_color;
    bool     m_fill;

};

#endif // VGRAPHICSFILLITEM_H
