<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogPatternProperties</class>
 <widget class="QDialog" name="DialogPatternProperties">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>492</width>
    <height>532</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Pattern preferences</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../../../libs/vmisc/share/resources/theme.qrc">
    <normaloff>:/icons/win.icon.theme/16x16/actions/preferences-other.png</normaloff>:/icons/win.icon.theme/16x16/actions/preferences-other.png</iconset>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_4">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tab">
      <attribute name="title">
       <string>Pattern</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayoutPath">
         <item>
          <widget class="QLabel" name="label_9">
           <property name="text">
            <string>Path:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditPathToFile">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
           <property name="styleSheet">
            <string notr="true">background: transparent;</string>
           </property>
           <property name="frame">
            <bool>false</bool>
           </property>
           <property name="readOnly">
            <bool>true</bool>
           </property>
           <property name="placeholderText">
            <string notr="true">Path to the pattern file.</string>
           </property>
          </widget>
         </item>
         <item alignment="Qt::AlignRight">
          <widget class="QPushButton" name="pushButtonShowInExplorer">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Show in Explorer</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="sizeConstraint">
          <enum>QLayout::SetMaximumSize</enum>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="text">
              <string>Pattern description</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPlainTextEdit" name="plainTextEditDescription">
             <property name="documentTitle">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_11">
           <property name="spacing">
            <number>6</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QLabel" name="imageLabel">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>200</horstretch>
               <verstretch>200</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>150</width>
               <height>200</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>200</width>
               <height>200</height>
              </size>
             </property>
             <property name="toolTip">
              <string>Call context menu for edit</string>
             </property>
             <property name="text">
              <string>No image</string>
             </property>
             <property name="textFormat">
              <enum>Qt::AutoText</enum>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
             <property name="margin">
              <number>1</number>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>For technical notes</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPlainTextEdit" name="plainTextEditTechNotes"/>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_2">
      <attribute name="title">
       <string>Heights and Sizes</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_5">
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="title">
          <string>Default height and size</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_9">
          <item>
           <widget class="QRadioButton" name="radioButtonDefFromM">
            <property name="text">
             <string>From multisize measurements</string>
            </property>
            <property name="checked">
             <bool>true</bool>
            </property>
            <attribute name="buttonGroup">
             <string notr="true">buttonGroup</string>
            </attribute>
           </widget>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_8">
            <item>
             <widget class="QRadioButton" name="radioButtonDefFromP">
              <property name="text">
               <string>Custom</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
              <attribute name="buttonGroup">
               <string notr="true">buttonGroup</string>
              </attribute>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_5">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_3">
                <item>
                 <widget class="QLabel" name="label_4">
                  <property name="text">
                   <string>Height:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBoxHeight">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_4">
                <item>
                 <widget class="QLabel" name="label_5">
                  <property name="text">
                   <string>Size:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBoxSize">
                  <property name="enabled">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_6">
           <item>
            <widget class="QCheckBox" name="checkBoxAllHeights">
             <property name="text">
              <string>All heights (cm)</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QFrame" name="frame">
             <property name="frameShape">
              <enum>QFrame::StyledPanel</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Raised</enum>
             </property>
             <layout class="QGridLayout" name="gridLayout_2">
              <property name="sizeConstraint">
               <enum>QLayout::SetMaximumSize</enum>
              </property>
              <item row="8" column="0">
               <widget class="QCheckBox" name="checkBoxH98">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">98</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="0" column="0">
               <widget class="QCheckBox" name="checkBoxH50">
                <property name="text">
                 <string notr="true">50</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QCheckBox" name="checkBoxH56">
                <property name="text">
                 <string notr="true">56</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QCheckBox" name="checkBoxH62">
                <property name="text">
                 <string notr="true">62</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QCheckBox" name="checkBoxH68">
                <property name="text">
                 <string notr="true">68</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QCheckBox" name="checkBoxH74">
                <property name="text">
                 <string notr="true">74</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="7" column="0">
               <widget class="QCheckBox" name="checkBoxH92">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">92</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="6" column="0">
               <widget class="QCheckBox" name="checkBoxH86">
                <property name="text">
                 <string notr="true">86</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="5" column="0">
               <widget class="QCheckBox" name="checkBoxH80">
                <property name="text">
                 <string notr="true">80</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QCheckBox" name="checkBoxH104">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>0</height>
                 </size>
                </property>
                <property name="text">
                 <string notr="true">104</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QCheckBox" name="checkBoxH110">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">110</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QCheckBox" name="checkBoxH116">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">116</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QCheckBox" name="checkBoxH122">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">122</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="4" column="1">
               <widget class="QCheckBox" name="checkBoxH128">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">128</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="5" column="1">
               <widget class="QCheckBox" name="checkBoxH134">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">134</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="6" column="1">
               <widget class="QCheckBox" name="checkBoxH140">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">140</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="7" column="1">
               <widget class="QCheckBox" name="checkBoxH146">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">146</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="8" column="1">
               <widget class="QCheckBox" name="checkBoxH152">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">152</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="0" column="2">
               <widget class="QCheckBox" name="checkBoxH158">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">158</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="2">
               <widget class="QCheckBox" name="checkBoxH164">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">164</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="2" column="2">
               <widget class="QCheckBox" name="checkBoxH170">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">170</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="3" column="2">
               <widget class="QCheckBox" name="checkBoxH176">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">176</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="4" column="2">
               <widget class="QCheckBox" name="checkBoxH182">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">182</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="5" column="2">
               <widget class="QCheckBox" name="checkBoxH188">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">188</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="6" column="2">
               <widget class="QCheckBox" name="checkBoxH194">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">194</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="7" column="2">
               <widget class="QCheckBox" name="checkBoxH200">
                <property name="text">
                 <string notr="true">200</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_7">
           <item>
            <widget class="QCheckBox" name="checkBoxAllSizes">
             <property name="text">
              <string>All sizes (cm)</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QFrame" name="frame_2">
             <property name="frameShape">
              <enum>QFrame::StyledPanel</enum>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Raised</enum>
             </property>
             <layout class="QGridLayout" name="gridLayout">
              <item row="3" column="1">
               <widget class="QCheckBox" name="checkBoxS46">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">46</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="4" column="1">
               <widget class="QCheckBox" name="checkBoxS48">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">48</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="8" column="0">
               <widget class="QCheckBox" name="checkBoxS38">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">38</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="0" column="0">
               <widget class="QCheckBox" name="checkBoxS22">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">22</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="0">
               <widget class="QCheckBox" name="checkBoxS24">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">24</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="5" column="1">
               <widget class="QCheckBox" name="checkBoxS50">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">50</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="7" column="0">
               <widget class="QCheckBox" name="checkBoxS36">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">36</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="8" column="1">
               <widget class="QCheckBox" name="checkBoxS56">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">56</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QCheckBox" name="checkBoxS26">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">26</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="4" column="0">
               <widget class="QCheckBox" name="checkBoxS30">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">30</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="7" column="1">
               <widget class="QCheckBox" name="checkBoxS54">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">54</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QCheckBox" name="checkBoxS40">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">40</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="6" column="1">
               <widget class="QCheckBox" name="checkBoxS52">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">52</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="5" column="0">
               <widget class="QCheckBox" name="checkBoxS32">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">32</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QCheckBox" name="checkBoxS42">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">42</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="6" column="0">
               <widget class="QCheckBox" name="checkBoxS34">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">34</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QCheckBox" name="checkBoxS44">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">44</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QCheckBox" name="checkBoxS28">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="sizePolicy">
                 <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                  <horstretch>0</horstretch>
                  <verstretch>0</verstretch>
                 </sizepolicy>
                </property>
                <property name="text">
                 <string notr="true">28</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="1" column="2">
               <widget class="QCheckBox" name="checkBoxS60">
                <property name="text">
                 <string notr="true">60</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="2" column="2">
               <widget class="QCheckBox" name="checkBoxS62">
                <property name="text">
                 <string notr="true">62</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="3" column="2">
               <widget class="QCheckBox" name="checkBoxS64">
                <property name="text">
                 <string notr="true">64</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="4" column="2">
               <widget class="QCheckBox" name="checkBoxS66">
                <property name="text">
                 <string notr="true">66</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="5" column="2">
               <widget class="QCheckBox" name="checkBoxS68">
                <property name="text">
                 <string notr="true">68</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="0" column="2">
               <widget class="QCheckBox" name="checkBoxS58">
                <property name="text">
                 <string notr="true">58</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="6" column="2">
               <widget class="QCheckBox" name="checkBoxS70">
                <property name="text">
                 <string notr="true">70</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item row="7" column="2">
               <widget class="QCheckBox" name="checkBoxS72">
                <property name="text">
                 <string notr="true">72</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_3">
      <attribute name="title">
       <string>Security</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_10">
       <item alignment="Qt::AlignTop">
        <widget class="QCheckBox" name="checkBoxPatternReadOnly">
         <property name="text">
          <string>Open only for read</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab_4">
      <attribute name="title">
       <string>Label data</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_13">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_19">
         <item>
          <widget class="QLabel" name="label_13">
           <property name="text">
            <string>Label template:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButtonEditPatternLabel">
           <property name="toolTip">
            <string>Edit pattern label</string>
           </property>
           <property name="text">
            <string>Edit template</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_4">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>1</verstretch>
          </sizepolicy>
         </property>
         <property name="title">
          <string>Label data</string>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_12">
          <item>
           <layout class="QFormLayout" name="formLayout">
            <item row="0" column="0">
             <widget class="QLabel" name="label_6">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Pattern name:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="VLineEdit" name="lineEditPatternName">
              <property name="maxLength">
               <number>30</number>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QLabel" name="label_7">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Pattern number:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="VLineEdit" name="lineEditPatternNumber">
              <property name="maxLength">
               <number>30</number>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="label_8">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Company/Designer name:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="2" column="1">
             <widget class="VLineEdit" name="lineEditCompanyName">
              <property name="maxLength">
               <number>30</number>
              </property>
             </widget>
            </item>
            <item row="3" column="0">
             <widget class="QLabel" name="label_10">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Customer name:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="3" column="1">
             <widget class="VLineEdit" name="lineEditCustomerName">
              <property name="maxLength">
               <number>30</number>
              </property>
             </widget>
            </item>
            <item row="4" column="0">
             <widget class="QLabel" name="label">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Date format:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="4" column="1">
             <widget class="QComboBox" name="comboBoxDateFormat"/>
            </item>
            <item row="5" column="0">
             <widget class="QLabel" name="label_11">
              <property name="minimumSize">
               <size>
                <width>140</width>
                <height>0</height>
               </size>
              </property>
              <property name="text">
               <string>Time format:</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item row="5" column="1">
             <widget class="QComboBox" name="comboBoxTimeFormat"/>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <widget class="QDialogButtonBox" name="buttonBox">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="standardButtons">
      <set>QDialogButtonBox::Apply|QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>VLineEdit</class>
   <extends>QLineEdit</extends>
   <header>vlineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../libs/vmisc/share/resources/theme.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>buttonBox</sender>
   <signal>accepted()</signal>
   <receiver>DialogPatternProperties</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>257</x>
     <y>682</y>
    </hint>
    <hint type="destinationlabel">
     <x>157</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>buttonBox</sender>
   <signal>rejected()</signal>
   <receiver>DialogPatternProperties</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>325</x>
     <y>682</y>
    </hint>
    <hint type="destinationlabel">
     <x>286</x>
     <y>274</y>
    </hint>
   </hints>
  </connection>
 </connections>
 <buttongroups>
  <buttongroup name="buttonGroup"/>
 </buttongroups>
</ui>
