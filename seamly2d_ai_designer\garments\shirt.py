"""
女式衬衫版型生成模块
包含前片、后片、袖子和领子的完整女式衬衫版型
"""

import os
import logging
from typing import Dict, Optional, Tuple, List
import matplotlib.pyplot as plt
import numpy as np
import math

from ..core.pattern_generator import Seamly2DPatternGenerator
from ..core.config import PatternConfig
from ..core.exceptions import PatternGenerationError, MeasurementError

logger = logging.getLogger(__name__)

class WomensShirt:
    """女式衬衫版型生成器 - 包含前片、后片、袖子和领子"""
    
    def __init__(self, config: Optional[PatternConfig] = None):
        """初始化女式衬衫版型生成器
        
        Args:
            config: 版型配置对象，如果为None则使用默认配置
        """
        self.config = config or PatternConfig()
        self.generator = Seamly2DPatternGenerator(self.config)
        self.measurements = {}
        self.ease = 8.0  # 衬衫放松量通常较大
        self.name = "女式衬衫版型"
        self.description = "包含前片、后片、袖子和领子的完整女式衬衫版型"
        
        # 女式衬衫特有参数
        self.neck_depth_front = 8.0   # 前领深
        self.neck_depth_back = 2.5    # 后领深
        self.sleeve_length = 58.0     # 长袖长度
        self.armhole_depth = 22.0     # 袖窿深
        self.dart_length = 12.0       # 胸省长度
        self.collar_width = 7.0       # 领宽
        self.collar_height = 3.5      # 领高
        self.button_overlap = 2.5     # 门襟重叠
        
    def set_measurements(self, measurements: Dict[str, float]) -> 'WomensShirt':
        """设置测量数据
        
        Args:
            measurements: 测量数据字典
            
        Returns:
            self: 支持方法链式调用
        """
        self.measurements = measurements
        self.generator.set_measurements(measurements)
        return self
    
    def set_ease(self, ease: float) -> 'WomensShirt':
        """设置放松量
        
        Args:
            ease: 放松量(cm)
            
        Returns:
            self: 支持方法链式调用
        """
        self.ease = ease
        return self
    
    def set_sleeve_length(self, length: float) -> 'WomensShirt':
        """设置袖长
        
        Args:
            length: 袖长(cm)
            
        Returns:
            self: 支持方法链式调用
        """
        self.sleeve_length = length
        return self
    
    def set_dart_length(self, length: float) -> 'WomensShirt':
        """设置胸省长度
        
        Args:
            length: 胸省长度(cm)
            
        Returns:
            self: 支持方法链式调用
        """
        self.dart_length = length
        return self
    
    def create_pattern(self, output_file: str = "womens_shirt_pattern.sm2d", 
                      preview: bool = True) -> str:
        """创建女式衬衫版型
        
        Args:
            output_file: 输出文件路径
            preview: 是否生成预览图
            
        Returns:
            str: 版型文件路径
        """
        # 检查必要的测量数据
        required = ["bust", "shoulder_width", "length", "waist"]
        for field in required:
            if field not in self.measurements:
                raise MeasurementError(f"缺少必要的测量数据: {field}")
        
        # 提取关键尺寸
        bust = self.measurements["bust"]
        shoulder_width = self.measurements["shoulder_width"]
        length = self.measurements["length"]
        waist = self.measurements["waist"]
        
        # 应用放松量
        bust_with_ease = bust/2 + self.ease
        waist_with_ease = waist/2 + self.ease/2
        
        # 创建新版型
        self.generator.create_new_pattern(self.name, self.description)
        
        # 添加衬衫特有的增量变量
        self.generator.add_increment("#bustWithEase", str(bust_with_ease), "胸围/2+放松量")
        self.generator.add_increment("#waistWithEase", str(waist_with_ease), "腰围/2+放松量")
        self.generator.add_increment("#neckDepthFront", str(self.neck_depth_front), "前领深")
        self.generator.add_increment("#neckDepthBack", str(self.neck_depth_back), "后领深")
        self.generator.add_increment("#sleeveLength", str(self.sleeve_length), "袖长")
        self.generator.add_increment("#armholeDepth", str(self.armhole_depth), "袖窿深")
        self.generator.add_increment("#dartLength", str(self.dart_length), "胸省长度")
        self.generator.add_increment("#collarWidth", str(self.collar_width), "领宽")
        self.generator.add_increment("#buttonOverlap", str(self.button_overlap), "门襟重叠")
        
        # 创建各个部件
        self._create_front_piece()
        self._create_back_piece()
        self._create_sleeve()
        self._create_collar()
        
        # 保存版型
        pattern_file = self.generator.save_pattern(output_file)
        
        # 生成预览图
        if preview:
            preview_file = output_file.replace(".sm2d", "_preview.png")
            self.generate_preview(preview_file)
        
        return pattern_file
    
    def _create_front_piece(self) -> None:
        """创建前片（包含胸省和门襟）"""
        # 前片基本点
        p1 = self.generator.add_point("0", "0", "A_前肩左", "base")
        p2 = self.generator.add_point("#shoulder_width", "0", "B_前肩右")
        p3 = self.generator.add_point("0", "#length", "C_前下摆左")
        p4 = self.generator.add_point("#bustWithEase + #buttonOverlap", "#length", "D_前下摆右")
        
        # 袖窿点
        p5 = self.generator.add_point("0", "#armholeDepth", "E_前袖窿左")
        p6 = self.generator.add_point("#bustWithEase", "#armholeDepth", "F_前袖窿右")
        
        # 领口点
        p7 = self.generator.add_point("#shoulder_width/3", "0", "G_前领右")
        p8 = self.generator.add_point("#buttonOverlap", "#neckDepthFront", "H_前领底")
        
        # 腰线点
        p9 = self.generator.add_point("0", "#length * 0.6", "I_前腰左")
        p10 = self.generator.add_point("#waistWithEase + #buttonOverlap", "#length * 0.6", "J_前腰右")
        
        # 胸省点
        dart_center = "#bustWithEase * 0.4"
        p11 = self.generator.add_point(dart_center, "#armholeDepth + 5", "K_胸省顶")
        p12 = self.generator.add_point(f"{dart_center} - 2", "#armholeDepth + #dartLength", "L_胸省左")
        p13 = self.generator.add_point(f"{dart_center} + 2", "#armholeDepth + #dartLength", "M_胸省右")
        
        # 门襟线
        p14 = self.generator.add_point("#buttonOverlap", "0", "N_门襟上")
        p15 = self.generator.add_point("#buttonOverlap", "#length", "O_门襟下")
        
        # 添加线条
        self.generator.add_line(p8, p5, "前左侧上")
        self.generator.add_line(p5, p9, "前左侧中")
        self.generator.add_line(p9, p3, "前左侧下")
        self.generator.add_line(p3, p15, "前下摆左")
        self.generator.add_line(p15, p4, "前下摆右")
        self.generator.add_line(p4, p10, "前右侧下")
        self.generator.add_line(p10, p6, "前右侧中")
        self.generator.add_line(p6, p2, "前右侧上")
        self.generator.add_line(p2, p7, "前右肩")
        self.generator.add_line(p1, p7, "前左肩")
        self.generator.add_line(p7, p8, "前领口")
        self.generator.add_line(p14, p15, "门襟线")
        self.generator.add_line(p11, p12, "胸省左")
        self.generator.add_line(p11, p13, "胸省右")
        
        # 创建前片
        self.generator.create_piece("前片", [p1, p7, p8, p5, p9, p3, p15, p4, p10, p6, p2], True)
    
    def _create_back_piece(self) -> None:
        """创建后片"""
        # 后片基本点 (X坐标偏移)
        offset_x = "#bustWithEase + #buttonOverlap + 15"
        
        p1 = self.generator.add_point(offset_x, "0", "A_后肩左")
        p2 = self.generator.add_point(f"{offset_x} + #shoulder_width", "0", "B_后肩右")
        p3 = self.generator.add_point(offset_x, "#length", "C_后下摆左")
        p4 = self.generator.add_point(f"{offset_x} + #bustWithEase", "#length", "D_后下摆右")
        
        # 袖窿点
        p5 = self.generator.add_point(offset_x, "#armholeDepth", "E_后袖窿左")
        p6 = self.generator.add_point(f"{offset_x} + #bustWithEase", "#armholeDepth", "F_后袖窿右")
        
        # 领口点
        p7 = self.generator.add_point(f"{offset_x} + #shoulder_width/3", "0", "G_后领右")
        p8 = self.generator.add_point(offset_x, "#neckDepthBack", "H_后领底")
        
        # 腰线点
        p9 = self.generator.add_point(offset_x, "#length * 0.6", "I_后腰左")
        p10 = self.generator.add_point(f"{offset_x} + #waistWithEase", "#length * 0.6", "J_后腰右")
        
        # 添加线条
        self.generator.add_line(p8, p5, "后左侧上")
        self.generator.add_line(p5, p9, "后左侧中")
        self.generator.add_line(p9, p3, "后左侧下")
        self.generator.add_line(p3, p4, "后下摆")
        self.generator.add_line(p4, p10, "后右侧下")
        self.generator.add_line(p10, p6, "后右侧中")
        self.generator.add_line(p6, p2, "后右侧上")
        self.generator.add_line(p2, p7, "后右肩")
        self.generator.add_line(p1, p7, "后左肩")
        self.generator.add_line(p7, p8, "后领口")
        
        # 创建后片
        self.generator.create_piece("后片", [p1, p7, p8, p5, p9, p3, p4, p10, p6, p2], False)
    
    def _create_sleeve(self) -> None:
        """创建长袖"""
        # 袖子基本点 (Y坐标偏移)
        offset_y = "#length + 20"
        sleeve_width = "#armholeDepth * 1.4"
        
        p1 = self.generator.add_point("0", offset_y, "A_袖山左")
        p2 = self.generator.add_point(sleeve_width, offset_y, "B_袖山右")
        p3 = self.generator.add_point("5", f"{offset_y} + #sleeveLength", "C_袖口左")
        p4 = self.generator.add_point(f"{sleeve_width} - 5", f"{offset_y} + #sleeveLength", "D_袖口右")
        
        # 袖山顶点
        p5 = self.generator.add_point(f"{sleeve_width}/2", f"{offset_y} - 10", "E_袖山顶")
        
        # 袖口收缩点
        p6 = self.generator.add_point("10", f"{offset_y} + #sleeveLength - 15", "F_袖口收缩左")
        p7 = self.generator.add_point(f"{sleeve_width} - 10", f"{offset_y} + #sleeveLength - 15", "G_袖口收缩右")
        
        # 添加线条
        self.generator.add_line(p1, p5, "袖山左")
        self.generator.add_line(p5, p2, "袖山右")
        self.generator.add_line(p2, p7, "袖右侧上")
        self.generator.add_line(p7, p4, "袖右侧下")
        self.generator.add_line(p4, p3, "袖口")
        self.generator.add_line(p3, p6, "袖左侧下")
        self.generator.add_line(p6, p1, "袖左侧上")
        
        # 创建袖子
        self.generator.create_piece("袖子", [p1, p5, p2, p7, p4, p3, p6], False)
    
    def _create_collar(self) -> None:
        """创建衬衫领"""
        # 领子基本点 (单独放置)
        collar_offset_x = "#bustWithEase * 2 + 40"
        collar_offset_y = "10"
        collar_length = "#shoulder_width * 2 + #neckDepthFront + #neckDepthBack"
        
        p1 = self.generator.add_point(collar_offset_x, collar_offset_y, "A_领底左")
        p2 = self.generator.add_point(f"{collar_offset_x} + {collar_length}", collar_offset_y, "B_领底右")
        p3 = self.generator.add_point(collar_offset_x, f"{collar_offset_y} + #collarWidth", "C_领上左")
        p4 = self.generator.add_point(f"{collar_offset_x} + {collar_length}", f"{collar_offset_y} + #collarWidth", "D_领上右")
        
        # 领尖点
        p5 = self.generator.add_point(f"{collar_offset_x} + 8", f"{collar_offset_y} + #collarWidth + 5", "E_领尖左")
        p6 = self.generator.add_point(f"{collar_offset_x} + {collar_length} - 8", f"{collar_offset_y} + #collarWidth + 5", "F_领尖右")
        
        # 添加线条
        self.generator.add_line(p1, p2, "领底线")
        self.generator.add_line(p2, p4, "领右侧")
        self.generator.add_line(p4, p6, "领上右")
        self.generator.add_line(p6, p5, "领上线")
        self.generator.add_line(p5, p3, "领上左")
        self.generator.add_line(p3, p1, "领左侧")
        
        # 创建领子
        self.generator.create_piece("领子", [p1, p2, p4, p6, p5, p3], False)
    
    def generate_preview(self, output_file: str = "womens_shirt_preview.png") -> str:
        """生成女式衬衫预览图
        
        Args:
            output_file: 输出文件路径
            
        Returns:
            str: 输出文件的绝对路径
        """
        fig, ax = plt.subplots(figsize=(20, 14))
        
        # 提取关键尺寸
        bust = self.measurements.get("bust", 88.0)
        shoulder_width = self.measurements.get("shoulder_width", 38.0)
        length = self.measurements.get("length", 65.0)
        waist = self.measurements.get("waist", 68.0)
        
        # 应用放松量
        bust_with_ease = bust/2 + self.ease
        waist_with_ease = waist/2 + self.ease/2
        
        # 绘制各个部件
        self._draw_front_piece(ax, bust_with_ease, waist_with_ease, shoulder_width, length)
        self._draw_back_piece(ax, bust_with_ease, waist_with_ease, shoulder_width, length)
        self._draw_sleeve(ax, length)
        self._draw_collar(ax, shoulder_width)
        
        # 设置图表
        ax.set_aspect('equal')
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.set_title('女式衬衫版型预览 - 带详细尺寸标注', fontsize=18, fontweight='bold')
        ax.set_xlabel('宽度 (cm)', fontsize=14)
        ax.set_ylabel('长度 (cm)', fontsize=14)
        
        # 设置坐标范围
        total_width = bust_with_ease * 3 + 80
        total_height = length + self.sleeve_length + 40
        ax.set_xlim(-10, total_width)
        ax.set_ylim(-20, total_height)
        
        # 反转Y轴以匹配Seamly2D坐标系
        ax.invert_yaxis()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=200, bbox_inches='tight')
        plt.close()
        
        logger.info(f"女式衬衫预览图已保存到: {output_file}")
        return os.path.abspath(output_file)
    
    def _draw_front_piece(self, ax, bust_with_ease, waist_with_ease, shoulder_width, length):
        """绘制前片"""
        # 前片轮廓点
        front_points = [
            (0, 0),  # A_前肩左
            (shoulder_width/3, 0),  # G_前领右
            (self.button_overlap, self.neck_depth_front),  # H_前领底
            (0, self.armhole_depth),  # E_前袖窿左
            (0, length * 0.6),  # I_前腰左
            (0, length),  # C_前下摆左
            (self.button_overlap, length),  # O_门襟下
            (bust_with_ease + self.button_overlap, length),  # D_前下摆右
            (waist_with_ease + self.button_overlap, length * 0.6),  # J_前腰右
            (bust_with_ease, self.armhole_depth),  # F_前袖窿右
            (shoulder_width, 0),  # B_前肩右
        ]
        
        # 绘制轮廓
        for i in range(len(front_points)):
            p1 = front_points[i]
            p2 = front_points[(i + 1) % len(front_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'b-', linewidth=2.5)
        
        # 绘制胸省
        dart_center = bust_with_ease * 0.4
        dart_points = [
            (dart_center, self.armhole_depth + 5),
            (dart_center - 2, self.armhole_depth + self.dart_length),
            (dart_center + 2, self.armhole_depth + self.dart_length)
        ]
        ax.plot([dart_points[0][0], dart_points[1][0]], [dart_points[0][1], dart_points[1][1]], 'b--', linewidth=2)
        ax.plot([dart_points[0][0], dart_points[2][0]], [dart_points[0][1], dart_points[2][1]], 'b--', linewidth=2)
        
        # 绘制门襟线
        ax.plot([self.button_overlap, self.button_overlap], [0, length], 'b:', linewidth=2)
        
        # 添加详细尺寸标注
        self._add_front_dimensions(ax, bust_with_ease, waist_with_ease, shoulder_width, length)
        
        # 添加标题
        ax.text(bust_with_ease/2, -5, "前片", ha='center', fontsize=14, fontweight='bold', color='blue')
    
    def _draw_back_piece(self, ax, bust_with_ease, waist_with_ease, shoulder_width, length):
        """绘制后片"""
        offset_x = bust_with_ease + self.button_overlap + 15
        
        # 后片轮廓点
        back_points = [
            (offset_x, 0),  # A_后肩左
            (offset_x + shoulder_width/3, 0),  # G_后领右
            (offset_x, self.neck_depth_back),  # H_后领底
            (offset_x, self.armhole_depth),  # E_后袖窿左
            (offset_x, length * 0.6),  # I_后腰左
            (offset_x, length),  # C_后下摆左
            (offset_x + bust_with_ease, length),  # D_后下摆右
            (offset_x + waist_with_ease, length * 0.6),  # J_后腰右
            (offset_x + bust_with_ease, self.armhole_depth),  # F_后袖窿右
            (offset_x + shoulder_width, 0),  # B_后肩右
        ]
        
        # 绘制轮廓
        for i in range(len(back_points)):
            p1 = back_points[i]
            p2 = back_points[(i + 1) % len(back_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'g-', linewidth=2.5)
        
        # 添加详细尺寸标注
        self._add_back_dimensions(ax, offset_x, bust_with_ease, waist_with_ease, shoulder_width, length)
        
        # 添加标题
        ax.text(offset_x + bust_with_ease/2, -5, "后片", ha='center', fontsize=14, fontweight='bold', color='green')
    
    def _draw_sleeve(self, ax, length):
        """绘制袖子"""
        offset_y = length + 20
        sleeve_width = self.armhole_depth * 1.4
        
        # 袖子轮廓点
        sleeve_points = [
            (0, offset_y),  # A_袖山左
            (sleeve_width/2, offset_y - 10),  # E_袖山顶
            (sleeve_width, offset_y),  # B_袖山右
            (sleeve_width - 10, offset_y + self.sleeve_length - 15),  # G_袖口收缩右
            (sleeve_width - 5, offset_y + self.sleeve_length),  # D_袖口右
            (5, offset_y + self.sleeve_length),  # C_袖口左
            (10, offset_y + self.sleeve_length - 15),  # F_袖口收缩左
        ]
        
        # 绘制轮廓
        for i in range(len(sleeve_points)):
            p1 = sleeve_points[i]
            p2 = sleeve_points[(i + 1) % len(sleeve_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'r-', linewidth=2.5)
        
        # 添加详细尺寸标注
        self._add_sleeve_dimensions(ax, offset_y, sleeve_width)
        
        # 添加标题
        ax.text(sleeve_width/2, offset_y + self.sleeve_length + 5, "袖子", 
               ha='center', fontsize=14, fontweight='bold', color='red')
    
    def _draw_collar(self, ax, shoulder_width):
        """绘制领子"""
        collar_offset_x = shoulder_width * 2 + 80
        collar_offset_y = 10
        collar_length = shoulder_width * 1.5
        
        # 领子轮廓点
        collar_points = [
            (collar_offset_x, collar_offset_y),  # A_领底左
            (collar_offset_x + collar_length, collar_offset_y),  # B_领底右
            (collar_offset_x + collar_length, collar_offset_y + self.collar_width),  # D_领上右
            (collar_offset_x + collar_length - 8, collar_offset_y + self.collar_width + 5),  # F_领尖右
            (collar_offset_x + 8, collar_offset_y + self.collar_width + 5),  # E_领尖左
            (collar_offset_x, collar_offset_y + self.collar_width),  # C_领上左
        ]
        
        # 绘制轮廓
        for i in range(len(collar_points)):
            p1 = collar_points[i]
            p2 = collar_points[(i + 1) % len(collar_points)]
            ax.plot([p1[0], p2[0]], [p1[1], p2[1]], 'm-', linewidth=2.5)
        
        # 添加详细尺寸标注
        self._add_collar_dimensions(ax, collar_offset_x, collar_offset_y, collar_length)
        
        # 添加标题
        ax.text(collar_offset_x + collar_length/2, collar_offset_y + self.collar_width + 10, "领子", 
               ha='center', fontsize=14, fontweight='bold', color='magenta')
    
    def _add_front_dimensions(self, ax, bust_with_ease, waist_with_ease, shoulder_width, length):
        """添加前片尺寸标注"""
        # 胸围标注
        ax.annotate('', xy=(0, self.armhole_depth), xytext=(bust_with_ease, self.armhole_depth),
                   arrowprops=dict(arrowstyle='<->', color='blue', lw=1.5))
        ax.text(bust_with_ease/2, self.armhole_depth - 2, f'胸围/2+放松量: {bust_with_ease:.1f}cm', 
               ha='center', va='top', fontsize=10, fontweight='bold', color='blue')
        
        # 衣长标注
        ax.annotate('', xy=(-3, 0), xytext=(-3, length),
                   arrowprops=dict(arrowstyle='<->', color='blue', lw=1.5))
        ax.text(-5, length/2, f'衣长: {length:.1f}cm', ha='right', va='center', 
               rotation=90, fontsize=10, fontweight='bold', color='blue')
        
        # 肩宽标注
        ax.annotate('', xy=(0, -2), xytext=(shoulder_width, -2),
                   arrowprops=dict(arrowstyle='<->', color='blue', lw=1.5))
        ax.text(shoulder_width/2, -4, f'肩宽: {shoulder_width:.1f}cm', 
               ha='center', va='top', fontsize=10, fontweight='bold', color='blue')
        
        # 袖窿深标注
        ax.annotate('', xy=(bust_with_ease + 2, 0), xytext=(bust_with_ease + 2, self.armhole_depth),
                   arrowprops=dict(arrowstyle='<->', color='blue', lw=1.5))
        ax.text(bust_with_ease + 4, self.armhole_depth/2, f'袖窿深: {self.armhole_depth:.1f}cm', 
               ha='left', va='center', rotation=90, fontsize=10, fontweight='bold', color='blue')
        
        # 领深标注
        ax.text(self.button_overlap + 1, self.neck_depth_front/2, f'前领深: {self.neck_depth_front:.1f}cm', 
               ha='left', va='center', fontsize=9, fontweight='bold', color='blue')
        
        # 门襟标注
        ax.text(self.button_overlap/2, length/2, f'门襟: {self.button_overlap:.1f}cm', 
               ha='center', va='center', rotation=90, fontsize=9, fontweight='bold', color='blue')
    
    def _add_back_dimensions(self, ax, offset_x, bust_with_ease, waist_with_ease, shoulder_width, length):
        """添加后片尺寸标注"""
        # 胸围标注
        ax.text(offset_x + bust_with_ease/2, self.armhole_depth - 2, f'胸围/2+放松量: {bust_with_ease:.1f}cm', 
               ha='center', va='top', fontsize=10, fontweight='bold', color='green')
        
        # 后领深标注
        ax.text(offset_x + 1, self.neck_depth_back/2, f'后领深: {self.neck_depth_back:.1f}cm', 
               ha='left', va='center', fontsize=9, fontweight='bold', color='green')
    
    def _add_sleeve_dimensions(self, ax, offset_y, sleeve_width):
        """添加袖子尺寸标注"""
        # 袖长标注
        ax.annotate('', xy=(-3, offset_y), xytext=(-3, offset_y + self.sleeve_length),
                   arrowprops=dict(arrowstyle='<->', color='red', lw=1.5))
        ax.text(-5, offset_y + self.sleeve_length/2, f'袖长: {self.sleeve_length:.1f}cm', 
               ha='right', va='center', rotation=90, fontsize=10, fontweight='bold', color='red')
        
        # 袖宽标注
        ax.text(sleeve_width/2, offset_y - 2, f'袖宽: {sleeve_width:.1f}cm', 
               ha='center', va='top', fontsize=10, fontweight='bold', color='red')
    
    def _add_collar_dimensions(self, ax, offset_x, offset_y, collar_length):
        """添加领子尺寸标注"""
        # 领长标注
        ax.text(offset_x + collar_length/2, offset_y - 2, f'领长: {collar_length:.1f}cm', 
               ha='center', va='top', fontsize=10, fontweight='bold', color='magenta')
        
        # 领宽标注
        ax.text(offset_x - 2, offset_y + self.collar_width/2, f'领宽: {self.collar_width:.1f}cm', 
               ha='right', va='center', rotation=90, fontsize=10, fontweight='bold', color='magenta') 