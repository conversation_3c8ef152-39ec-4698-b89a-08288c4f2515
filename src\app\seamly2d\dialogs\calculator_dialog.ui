<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CalculatorDialog</class>
 <widget class="QDialog" name="CalculatorDialog">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>280</width>
    <height>275</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>280</width>
    <height>275</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Calculator</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icon/64x64/patternist_draft_64x64.png</normaloff>:/icon/64x64/patternist_draft_64x64.png</iconset>
  </property>
  <property name="layoutDirection">
   <enum>Qt::LeftToRight</enum>
  </property>
  <layout class="QFormLayout" name="formLayout">
   <property name="sizeConstraint">
    <enum>QLayout::SetMaximumSize</enum>
   </property>
   <item row="0" column="0">
    <layout class="QVBoxLayout" name="calculatorLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="sizeConstraint">
      <enum>QLayout::SetMaximumSize</enum>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
